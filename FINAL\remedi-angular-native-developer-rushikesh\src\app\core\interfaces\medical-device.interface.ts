/**
 * Medical Device Communication Interfaces
 * Enterprise-level TypeScript interfaces for cross-platform medical device communication
 */

export enum PlatformType {
    ANDROID = 'android',
    WEB = 'web',
    ELECTRON = 'electron'
}

export enum DeviceType {
    PULSE_OXIMETER = 'pulse_oximeter',
    STETHOSCOPE = 'stethoscope',
    THERMOMETER = 'THERMOMETER',
    BLOOD_PRESSURE = 'blood_pressure',
    ECG = 'ecg',
    OPTICAL_READER='optical_reader',
    HEMOGLOBIN = 'hemoglobin' // ← ADD THIS
}

export enum CommunicationMethod {
    ANDROID_INTENT = 'android_intent',
    WEBSOCKET = 'websocket',
    BLUETOOTH = 'bluetooth'
}

export interface DeviceConnectionConfig {
    deviceType: DeviceType;
    platform: PlatformType;
    communicationMethod: CommunicationMethod;
    timeout?: number;
    retryAttempts?: number;
}

export interface AndroidIntentConfig {
    packageName: string;
    className: string;
    activityName: string;
    extras?: { [key: string]: any };
}

export interface WebSocketConfig {
    url: string;
    protocols?: string[];
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
}

export interface DeviceLaunchOptions {
    deviceType: DeviceType;
    patientId: string;
    realId?: string;
    language?: string;
    sessionId?: string;
    additionalParams?: { [key: string]: any };
}

export interface DeviceDataResponse {
    deviceType: DeviceType;
    timestamp: number;
    sessionId: string;
    patientId: string;
    data: any;
    status: 'success' | 'error' | 'cancelled';
    errorMessage?: string;
}

export interface PulseOximeterData {
    spo2: number;
    pulseRate: number;
    timestamp: number;
    batteryLevel?: number;
    signalQuality?: number;
}

export interface StethoscopeData {
    audioData: ArrayBuffer;
    duration: number;
    sampleRate: number;
    timestamp: number;
    heartRate?: number;
    fileName?: string;
    value?: string;
}

// export interface ThermometerData {
//     temperature: number;
//     unit: 'celsius' | 'fahrenheit';
//     timestamp: number;
//     batteryLevel?: number;
//     sensorType?: string;
// }
// export interface ThermometerData {
//   fahrenheit?: number;
//   celcius?: number;
//   timestamp: number;
//   source?: string;
//   batteryLevel?: number;
//   sensorType?: string;
//   error?: string;

//   /** ✅ Add this line to fix your issue */
//   unit?: 'celsius' | 'fahrenheit';
// }

export interface ThermometerData {
  temperature: number;
  unit: 'celsius' | 'fahrenheit';
  timestamp: number;
  batteryLevel?: number;
  signalQuality?: number;
  source: string;
  error?: string;
  sensorType?: string; // <-- Add this line
}



export interface OpticalReaderData {
  success: boolean;
  device_type: string;
  sensor_type?: string;
  test_name: string;
  test_result: string;
  optical_image_path?: string;
  image_name?: string;
  image_file_uri?: string;
  image_uri_parsed?: boolean;
  session_id?: string;
  timestamp?: number;
  source?: string;
}


export interface BloodPressureData {
    systolic: number;
    diastolic: number;
    pulse: number;
    timestamp: number;
    batteryLevel?: number;
    sensorType?: string;
    error?: string;
}

export interface ECGData {
    waveformData: number[];
    heartRate: number;
    duration: number;
    timestamp: number;
    leads?: string[];
    fileName?: string;
    value?: string;
    sensorType?: string;
    leadData?: {
        lead1?: string;
        lead2?: string;
        lead3?: string;
        avr?: string;
        avl?: string;
        avf?: string;
        v1?: string;
        v2?: string;
        v3?: string;
        v4?: string;
        v5?: string;
        v6?: string;
    };
    
}
// ← NEW SECTION: Define the data structure for the Hemoglobin Meter
export interface HemoglobinMeterData {
    hemoglobin_value: string;
    
}
export interface DeviceCommunicationResult<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    platform: PlatformType;
    deviceType: DeviceType;
    timestamp: number;
}

export interface DeviceStatus {
    isConnected: boolean;
    batteryLevel?: number;
    signalStrength?: number;
    lastCommunication?: number;
    deviceInfo?: {
        model: string;
        version: string;
        serialNumber?: string;
    };
}

export abstract class DeviceCommunicationProvider {
    abstract platform: PlatformType;
    abstract supportedDevices: DeviceType[];

    abstract launchDevice(options: DeviceLaunchOptions): Promise<DeviceCommunicationResult>;
    abstract getDeviceStatus(deviceType: DeviceType): Promise<DeviceStatus>;
    abstract disconnect(deviceType: DeviceType): Promise<void>;
}
