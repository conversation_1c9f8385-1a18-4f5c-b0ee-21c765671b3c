package io.ionic.starter.services;

import android.content.Context;
import android.content.Intent;
import android.content.ComponentName;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.util.Log;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

/**
 * NovaICare Intent Service
 * Enterprise-level service for handling Android intent-based communication with NovaICare app
 */
public class NovaICareIntentService {
    
    private static final String TAG = "NovaICareIntentService";
    
    // NovaICare app configuration
    private static final String NOVA_ICARE_PACKAGE = "com.neurosynaptic.nova_icare";
    private static final String BASE_ACTIVITY_PATH = "com.neurosynaptic.ble.sensors";
    
    // Device-specific activity mappings (Updated to match NovaICare reference implementation)
    private static final Map<String, String> DEVICE_ACTIVITIES = new HashMap<String, String>() {{
        put("PULSE_OXIMETER", "com.neurosynaptic.ble.sensors.PulseOxiMeterSensor1");
        put("STETHOSCOPE", "com.neurosynaptic.usb.StethoSensor");
        put("THERMOMETER", "com.neurosynaptic.ble.sensors.TemperatureSensor1");
        put("BLOOD_PRESSURE", "com.neurosynaptic.ble.sensors.BloodPressureSensor1");
        put("ECG", "com.neurosynaptic.bluetooth.sensors.ECGSensor1");
        put("HEMOGLOBIN", "com.neurosynaptic.usb.HemoglobinSensor");
    }};
    
    // Intent extra keys (Updated to match NovaICare reference implementation exactly)
    private static final String EXTRA_USE_SENSOR = "USE_SENSOR";  // Fixed: uppercase to match reference
    private static final String EXTRA_CLASS_NAME = "class_name";
    private static final String EXTRA_PACKAGE_NAME = "package_name";
    private static final String EXTRA_LANGUAGE = "language";
    private static final String EXTRA_PATIENT_ID = "pid";
    private static final String EXTRA_USE_FLAG = "useflag";
    private static final String EXTRA_DEVICE_TYPE = "DEVICE_TYPE";
    private static final String EXTRA_SENSOR_MODE = "SENSOR_MODE";
    private static final String EXTRA_SESSION_ID = "session_id";
    private static final String EXTRA_TIMESTAMP = "timestamp";
    
    // Result keys (Updated to match NovaICare reference implementation)
    public static final String RESULT_SPO2 = "spo2";
    public static final String RESULT_PULSE_RATE = "pulse_rate";
    public static final String RESULT_CELCIUS = "celcius";
    public static final String RESULT_FAHRENHEIT = "fahrenheit";
    public static final String RESULT_SYSTOLIC = "systolic";
    public static final String RESULT_DIASTOLIC = "diastolic";
    public static final String RESULT_STETHO_READING = "Stetho_Reading";
    public static final String RESULT_STETHO_VALUE = "stetho_value";
    public static final String RESULT_ECG_LEAD1 = "ecg_lead1";
    public static final String RESULT_ECG_LEAD2 = "ecg_lead2";
    public static final String RESULT_ECG_LEAD3 = "ecg_lead3";
    public static final String RESULT_ECG_AVR = "ecg_avr";
    public static final String RESULT_ECG_AVL = "ecg_avl";
    public static final String RESULT_ECG_AVF = "ecg_avf";
    public static final String RESULT_ECG_V1 = "ecg_v1";
    public static final String RESULT_ECG_V2 = "ecg_v2";
    public static final String RESULT_ECG_V3 = "ecg_v3";
    public static final String RESULT_ECG_V4 = "ecg_v4";
    public static final String RESULT_ECG_V5 = "ecg_v5";
    public static final String RESULT_ECG_V6 = "ecg_v6";
    public static final String RESULT_ECG_VALUE = "ecg_value";
    public static final String RESULT_FILE_NAME = "file_name";
    public static final String RESULT_SENSOR_TYPE = "sensor_type";
    public static final String RESULT_ERROR = "error";
    public static final String RESULT_DEVICE_TYPE = "device_type";
    public static final String RESULT_SESSION_ID = "session_id";
    
    private final Context context;
    
    public NovaICareIntentService(Context context) {
        this.context = context;
    }
    
    /**
     * Check if NovaICare app is installed
     */
    public boolean isNovaICareInstalled() {
        try {
            PackageManager pm = context.getPackageManager();
            pm.getPackageInfo(NOVA_ICARE_PACKAGE, PackageManager.GET_ACTIVITIES);
            Log.d(TAG, "✅ NovaICare app is installed");
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            Log.w(TAG, "❌ NovaICare app is not installed");
            return false;
        }
    }
    
    /**
     * Check if specific device activity is available
     * No fallback - each device must have its own activity
     */
    public boolean isDeviceActivityAvailable(String deviceType) {
        String activityName = DEVICE_ACTIVITIES.get(deviceType);
        if (activityName == null) {
            Log.w(TAG, "❌ Unknown device type: " + deviceType);
            return false;
        }
        
        try {
            Intent intent = new Intent();
            intent.setComponent(new ComponentName(NOVA_ICARE_PACKAGE, activityName));
            
            PackageManager pm = context.getPackageManager();
            List<ResolveInfo> activities = pm.queryIntentActivities(intent, 0);
            
            boolean available = !activities.isEmpty();
            Log.d(TAG, String.format("%s Device activity %s: %s", 
                available ? "✅" : "❌", deviceType, activityName));
            
            return available;
        } catch (Exception e) {
            Log.e(TAG, "❌ Error checking device activity availability: " + deviceType, e);
            return false;
        }
    }
    
    /**
     * Create intent for launching NovaICare device
     * Each device must have its own specific activity - no fallback
     */
    public Intent createDeviceIntent(DeviceLaunchRequest request) throws IntentCreationException {
        try {
            Log.d(TAG, "🚀 Creating device intent for: " + request.deviceType);
            
            // Validate device type
            String activityName = DEVICE_ACTIVITIES.get(request.deviceType);
            if (activityName == null) {
                throw new IntentCreationException("Unsupported device type: " + request.deviceType);
            }
            
            // Check if NovaICare is installed
            if (!isNovaICareInstalled()) {
                throw new IntentCreationException("NovaICare app is not installed");
            }
            
            // Check if specific device activity is available
            if (!isDeviceActivityAvailable(request.deviceType)) {
                throw new IntentCreationException("Device activity not available: " + request.deviceType + " (" + activityName + ")");
            }
            
            // Create intent with specific activity
            Intent intent = new Intent();
            intent.setComponent(new ComponentName(NOVA_ICARE_PACKAGE, activityName));
            
            // Add FLAG_ACTIVITY_NEW_TASK as per reference implementation
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            
            // Add standard extras (exact format from reference implementation)
            intent.putExtra(EXTRA_USE_SENSOR, true);
            intent.putExtra(EXTRA_CLASS_NAME, request.className);
            intent.putExtra(EXTRA_PACKAGE_NAME, request.packageName);
            intent.putExtra(EXTRA_LANGUAGE, request.language);
            intent.putExtra(EXTRA_PATIENT_ID, request.patientId);
            intent.putExtra(EXTRA_USE_FLAG, "0");
            intent.putExtra(EXTRA_DEVICE_TYPE, request.deviceType);
            intent.putExtra(EXTRA_SESSION_ID, request.sessionId);
            intent.putExtra(EXTRA_TIMESTAMP, System.currentTimeMillis());
            
            // Add device-specific extras
            addDeviceSpecificExtras(intent, request);
            
            // Add custom extras
            if (request.additionalExtras != null) {
                for (Map.Entry<String, Object> entry : request.additionalExtras.entrySet()) {
                    addExtraByType(intent, entry.getKey(), entry.getValue());
                }
            }
            
            Log.d(TAG, "✅ Device intent created successfully for: " + request.deviceType);
            return intent;
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Failed to create device intent: " + request.deviceType, e);
            throw new IntentCreationException("Failed to create intent: " + e.getMessage(), e);
        }
    }
    
    /**
     * Add device-specific extras to intent
     */
    private void addDeviceSpecificExtras(Intent intent, DeviceLaunchRequest request) {
        switch (request.deviceType) {
            case "PULSE_OXIMETER":
                intent.putExtra(EXTRA_SENSOR_MODE, "SPO2_PULSE");
                break;
                
            case "STETHOSCOPE":
                intent.putExtra(EXTRA_SENSOR_MODE, "AUDIO_RECORDING");
                break;
                
            case "THERMOMETER":
                intent.putExtra(EXTRA_SENSOR_MODE, "TEMPERATURE");
                break;
                
            case "BLOOD_PRESSURE":
                intent.putExtra(EXTRA_SENSOR_MODE, "BP_MEASUREMENT");
                break;
                
            case "ECG":
                intent.putExtra(EXTRA_SENSOR_MODE, "ECG_RECORDING");
                break;

            case "HEMOGLOBIN":
                intent.putExtra(EXTRA_SENSOR_MODE, "HEMOGLOBIN_MEASUREMENT");
                break;
                
            default:
                Log.w(TAG, "⚠️ No specific extras for device type: " + request.deviceType);
        }
    }
    
    /**
     * Add extra to intent based on value type
     */
    private void addExtraByType(Intent intent, String key, Object value) {
        if (value instanceof String) {
            intent.putExtra(key, (String) value);
        } else if (value instanceof Integer) {
            intent.putExtra(key, (Integer) value);
        } else if (value instanceof Boolean) {
            intent.putExtra(key, (Boolean) value);
        } else if (value instanceof Long) {
            intent.putExtra(key, (Long) value);
        } else if (value instanceof Float) {
            intent.putExtra(key, (Float) value);
        } else if (value instanceof Double) {
            intent.putExtra(key, (Double) value);
        } else {
            intent.putExtra(key, value.toString());
        }
    }
    
    /**
     * Parse result from NovaICare intent
     * Each device returns its own specific data format
     */
    public DeviceResult parseIntentResult(Intent data, String expectedDeviceType) {
        if (data == null) {
            Log.w(TAG, "❌ Intent result data is null");
            return DeviceResult.error("No result data received", expectedDeviceType);
        }
        
        try {
            Log.d(TAG, "📥 Parsing intent result for device: " + expectedDeviceType);
            
            DeviceResult.Builder resultBuilder = new DeviceResult.Builder()
                .setDeviceType(expectedDeviceType)
                .setTimestamp(System.currentTimeMillis());
            
            // Extract session ID if available
            String sessionId = data.getStringExtra(RESULT_SESSION_ID);
            if (sessionId != null) {
                resultBuilder.setSessionId(sessionId);
            }
            
            // Parse device-specific data based on expected device type
            switch (expectedDeviceType) {
                case "PULSE_OXIMETER":
                    return parsePulseOximeterResult(data, resultBuilder);
                    
                case "STETHOSCOPE":
                    return parseStethoscopeResult(data, resultBuilder);
                    
                case "THERMOMETER":
                    return parseThermometerResult(data, resultBuilder);
                    
                case "BLOOD_PRESSURE":
                    return parseBloodPressureResult(data, resultBuilder);
                    
                case "ECG":
                    return parseECGResult(data, resultBuilder);

                case "HEMOGLOBIN":
                    return parseHemoglobinResult(data, resultBuilder);
                    
                default:
                    Log.w(TAG, "⚠️ Unknown device type for result parsing: " + expectedDeviceType);
                    return parseGenericResult(data, resultBuilder);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Error parsing intent result", e);
            return DeviceResult.error("Failed to parse result: " + e.getMessage(), expectedDeviceType);
        }
    }
    
    /**
     * Parse pulse oximeter result (Updated to match NovaICare reference implementation)
     */
    private DeviceResult parsePulseOximeterResult(Intent data, DeviceResult.Builder builder) {
        String spo2 = data.getStringExtra(RESULT_SPO2);
        String pulseRate = data.getStringExtra(RESULT_PULSE_RATE);
        String error = data.getStringExtra(RESULT_ERROR);
        String fileName = data.getStringExtra(RESULT_FILE_NAME);
        String sensorType = data.getStringExtra(RESULT_SENSOR_TYPE);
        
        if (spo2 != null && pulseRate != null) {
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("spo2", spo2);
            resultData.put("pulse_rate", pulseRate);
            if (error != null) {
                resultData.put("error", error);
            }
            if (fileName != null) {
                resultData.put("file_name", fileName);
            }
            if (sensorType != null) {
                resultData.put("sensor_type", sensorType);
            }
            
            return builder.setSuccess(true).setData(resultData).build();
        } else {
            return builder.setSuccess(false)
                .setError("Missing SPO2 or pulse rate data")
                .build();
        }
    }
    
    /**
     * Parse stethoscope result (Updated to match NovaICare reference implementation)
     */
    private DeviceResult parseStethoscopeResult(Intent data, DeviceResult.Builder builder) {
        String fileName = data.getStringExtra(RESULT_STETHO_READING);
        String value = data.getStringExtra(RESULT_STETHO_VALUE);
        String sensorType = data.getStringExtra(RESULT_SENSOR_TYPE);
        
        Map<String, Object> resultData = new HashMap<>();
        if (fileName != null) {
            resultData.put("Stetho_Reading", fileName);
        }
        if (value != null) {
            resultData.put("stetho_value", value);
        }
        if (sensorType != null) {
            resultData.put("sensor_type", sensorType);
        }
        
        return builder.setSuccess(true).setData(resultData).build();
    }
    
    /**
     * Parse thermometer result (Updated to match NovaICare reference implementation)
     */
    private DeviceResult parseThermometerResult(Intent data, DeviceResult.Builder builder) {
        String celcius = data.getStringExtra(RESULT_CELCIUS);
        String fahrenheit = data.getStringExtra(RESULT_FAHRENHEIT);
        String sensorType = data.getStringExtra(RESULT_SENSOR_TYPE);
        
        if (celcius != null || fahrenheit != null) {
            Map<String, Object> resultData = new HashMap<>();
            if (celcius != null) {
                resultData.put("celcius", celcius);
            }
            if (fahrenheit != null) {
                resultData.put("fahrenheit", fahrenheit);
            }
            if (sensorType != null) {
                resultData.put("sensor_type", sensorType);
            }
            
            return builder.setSuccess(true).setData(resultData).build();
        } else {
            return builder.setSuccess(false)
                .setError("Missing temperature data")
                .build();
        }
    }
    
    /**
     * Parse blood pressure result (Updated to match NovaICare reference implementation)
     */
    private DeviceResult parseBloodPressureResult(Intent data, DeviceResult.Builder builder) {
        String systolic = data.getStringExtra(RESULT_SYSTOLIC);
        String diastolic = data.getStringExtra(RESULT_DIASTOLIC);
        String pulseRate = data.getStringExtra(RESULT_PULSE_RATE);
        String error = data.getStringExtra(RESULT_ERROR);
        String sensorType = data.getStringExtra(RESULT_SENSOR_TYPE);
        
        if (systolic != null && diastolic != null) {
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("systolic", systolic);
            resultData.put("diastolic", diastolic);
            if (pulseRate != null) {
                resultData.put("pulse_rate", pulseRate);
            }
            if (error != null) {
                resultData.put("error", error);
            }
            if (sensorType != null) {
                resultData.put("sensor_type", sensorType);
            }
            
            return builder.setSuccess(true).setData(resultData).build();
        } else {
            return builder.setSuccess(false)
                .setError("Missing blood pressure data")
                .build();
        }
    }
    
    /**
     * Parse ECG result (Updated to match NovaICare reference implementation)
     */
    private DeviceResult parseECGResult(Intent data, DeviceResult.Builder builder) {
        String pulseRate = data.getStringExtra(RESULT_PULSE_RATE);
        String lead1 = data.getStringExtra(RESULT_ECG_LEAD1);
        String lead2 = data.getStringExtra(RESULT_ECG_LEAD2);
        String lead3 = data.getStringExtra(RESULT_ECG_LEAD3);
        String avr = data.getStringExtra(RESULT_ECG_AVR);
        String avl = data.getStringExtra(RESULT_ECG_AVL);
        String avf = data.getStringExtra(RESULT_ECG_AVF);
        String v1 = data.getStringExtra(RESULT_ECG_V1);
        String v2 = data.getStringExtra(RESULT_ECG_V2);
        String v3 = data.getStringExtra(RESULT_ECG_V3);
        String v4 = data.getStringExtra(RESULT_ECG_V4);
        String v5 = data.getStringExtra(RESULT_ECG_V5);
        String v6 = data.getStringExtra(RESULT_ECG_V6);
        String fileName = data.getStringExtra(RESULT_FILE_NAME);
        String value = data.getStringExtra(RESULT_ECG_VALUE);
        String sensorType = data.getStringExtra(RESULT_SENSOR_TYPE);
        
        Map<String, Object> resultData = new HashMap<>();
        if (pulseRate != null) {
            resultData.put("pulse_rate", pulseRate);
        }
        if (lead1 != null) {
            resultData.put("ecg_lead1", lead1);
        }
        if (lead2 != null) {
            resultData.put("ecg_lead2", lead2);
        }
        if (lead3 != null) {
            resultData.put("ecg_lead3", lead3);
        }
        if (avr != null) {
            resultData.put("ecg_avr", avr);
        }
        if (avl != null) {
            resultData.put("ecg_avl", avl);
        }
        if (avf != null) {
            resultData.put("ecg_avf", avf);
        }
        if (v1 != null) {
            resultData.put("ecg_v1", v1);
        }
        if (v2 != null) {
            resultData.put("ecg_v2", v2);
        }
        if (v3 != null) {
            resultData.put("ecg_v3", v3);
        }
        if (v4 != null) {
            resultData.put("ecg_v4", v4);
        }
        if (v5 != null) {
            resultData.put("ecg_v5", v5);
        }
        if (v6 != null) {
            resultData.put("ecg_v6", v6);
        }
        if (fileName != null) {
            resultData.put("file_name", fileName);
        }
        if (value != null) {
            resultData.put("ecg_value", value);
        }
        if (sensorType != null) {
            resultData.put("sensor_type", sensorType);
        }
        
        return builder.setSuccess(true).setData(resultData).build();
    }
    /**
     * Parse hemoglobin result (Updated to match NovaICare reference implementation)
     */
    private DeviceResult parseHemoglobinResult(Intent data, DeviceResult.Builder builder) {
        // String hemoglobin = data.getStringExtra(HEMOGLOBIN_VALUE);
        Map<String, Object> resultData = new HashMap<>();
        Log.d(TAG, "Hemoglobin value: " + data);
        return builder.setSuccess(true).setData(resultData).build();
    }
    
    /**
     * Parse generic result
     */
    private DeviceResult parseGenericResult(Intent data, DeviceResult.Builder builder) {
        Map<String, Object> resultData = new HashMap<>();
        
        // Extract all string extras
        if (data.getExtras() != null) {
            for (String key : data.getExtras().keySet()) {
                Object value = data.getExtras().get(key);
                if (value != null) {
                    resultData.put(key, value);
                }
            }
        }
        
        return builder.setSuccess(true).setData(resultData).build();
    }
    
    /**
     * Get NovaICare app version
     */
    public String getNovaICareVersion() {
        try {
            PackageManager pm = context.getPackageManager();
            return pm.getPackageInfo(NOVA_ICARE_PACKAGE, 0).versionName;
        } catch (PackageManager.NameNotFoundException e) {
            return "Unknown";
        }
    }
    
    /**
     * Get supported device types
     */
    public String[] getSupportedDeviceTypes() {
        return DEVICE_ACTIVITIES.keySet().toArray(new String[0]);
    }
    
    /**
     * Custom exception for intent creation errors
     */
    public static class IntentCreationException extends Exception {
        public IntentCreationException(String message) {
            super(message);
        }
        
        public IntentCreationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
    
    /**
     * Device launch request data class
     */
    public static class DeviceLaunchRequest {
        public final String deviceType;
        public final String className;
        public final String packageName;
        public final String language;
        public final String patientId;
        public final String sessionId;
        public final Map<String, Object> additionalExtras;
        
        public DeviceLaunchRequest(String deviceType, String className, String packageName, 
                                 String language, String patientId, String sessionId,
                                 Map<String, Object> additionalExtras) {
            this.deviceType = deviceType;
            this.className = className;
            this.packageName = packageName;
            this.language = language;
            this.patientId = patientId;
            this.sessionId = sessionId;
            this.additionalExtras = additionalExtras;
        }
    }
    
    /**
     * Device result data class
     */
    public static class DeviceResult {
        public final boolean success;
        public final String deviceType;
        public final Map<String, Object> data;
        public final String error;
        public final String sessionId;
        public final long timestamp;
        
        private DeviceResult(boolean success, String deviceType, Map<String, Object> data, 
                           String error, String sessionId, long timestamp) {
            this.success = success;
            this.deviceType = deviceType;
            this.data = data;
            this.error = error;
            this.sessionId = sessionId;
            this.timestamp = timestamp;
        }
        
        public static DeviceResult error(String error, String deviceType) {
            return new Builder()
                .setSuccess(false)
                .setError(error)
                .setDeviceType(deviceType)
                .setTimestamp(System.currentTimeMillis())
                .build();
        }
        
        public static class Builder {
            private boolean success = false;
            private String deviceType;
            private Map<String, Object> data;
            private String error;
            private String sessionId;
            private long timestamp;
            
            public Builder setSuccess(boolean success) {
                this.success = success;
                return this;
            }
            
            public Builder setDeviceType(String deviceType) {
                this.deviceType = deviceType;
                return this;
            }
            
            public Builder setData(Map<String, Object> data) {
                this.data = data;
                return this;
            }
            
            public Builder setError(String error) {
                this.error = error;
                return this;
            }
            
            public Builder setSessionId(String sessionId) {
                this.sessionId = sessionId;
                return this;
            }
            
            public Builder setTimestamp(long timestamp) {
                this.timestamp = timestamp;
                return this;
            }
            
            public DeviceResult build() {
                return new DeviceResult(success, deviceType, data, error, sessionId, timestamp);
            }
        }
    }
}
