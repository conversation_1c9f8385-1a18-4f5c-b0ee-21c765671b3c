<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>iCare Device</ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="closeDialog()">
        ✕
      </ion-button>
    </ion-buttons>  
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  
  <!-- Test Type Selection -->
  <div class="ion-padding">
    <label class="select-label">SELECT TEST TYPE</label>
    
   

    <select        
    [(ngModel)]="selectedTestType"        
    (change)="onTestTypeChange($event)"        
    class="test-selector">       
    <option value="" disabled>Choose a test type</option>      
    <option value="HEPA_SCAN_HBsAg">{{ 'HEPA_SCAN_HBsAg' | translate }}</option>       
    <option value="HEPA_SCAN_HCV">{{ 'HEPA_SCAN_HCV' | translate }}</option>       
    <option value="SYPHILIS">{{ 'SYPHILIS' | translate }}</option>       
    <option value="TROPONIN_I">{{ 'TROPONIN_I' | translate }}</option>       
    <option value="MALERIA_P.f_P.v">{{ 'MALERIA_P.f_P.v' | translate }}</option>       
    <option value="MALERIA_P.f_PAN">{{ 'MALERIA_P.f_PAN' | translate }}</option>       
    <option value="DENGUE_IgG_IgM">{{ 'DENGUE_IgG_IgM' | translate }}</option>       
    <option value="DENGUE_NS1">{{ 'DENGUE_NS1' | translate }}</option>       
    <option value="PREGNANCY_HCG">{{ 'PREGNANCY_HCG' | translate }}</option>       
    <option value="HIV_TRILINE">{{ 'HIV_TRILINE' | translate }}</option>     
</select> 
    
    <!-- Show selected test type -->
    <div *ngIf="selectedTestType" class="selected-test ion-margin-top">
      <p><strong>Selected Test:</strong> {{ getTestTypeLabel(selectedTestType) }}</p>
    </div>
  </div>

  <!-- Launch Button -->
  <div class="ion-padding">
    <ion-button 
      (click)="launchRapidtest()" 
      expand="block" 
      size="large"
      class="launch-button"
      [disabled]="!selectedTestType">
      START OPTICAL READER
    </ion-button>
  </div>

  <!-- Test Results Section -->
  <div *ngIf="showResults" class="ion-padding">
    <h2>📊 Test Results</h2>
    
    <ion-item>
      <ion-label>
        <h3>Test Name:</h3>
        <p>{{ testName }}</p>
      </ion-label>
    </ion-item>
    
    <ion-item>
      <ion-label>
        <h3>Test Result:</h3>
        <p>{{ testResult }}</p>
      </ion-label>
    </ion-item>
    
    <ion-item>
      <ion-label>
        <h3>Image Name:</h3>
        <p>{{ imageName }}</p>
      </ion-label>
    </ion-item>
    
    <!-- Captured Image Section -->
    <div class="image-section">
      <h3>📷 Captured Image</h3>
      <div *ngIf="capturedImage; else noImageTemplate">
        <img 
          [src]="capturedImage" 
          alt="Captured test image"
          (load)="onImageLoad()"
          (error)="onImageError()"
          style="max-width: 100%; height: auto; border-radius: 8px;">
      </div>
      <ng-template #noImageTemplate>
        <p>📷 No image data available</p>
      </ng-template>
    </div>
  </div>

</ion-content>