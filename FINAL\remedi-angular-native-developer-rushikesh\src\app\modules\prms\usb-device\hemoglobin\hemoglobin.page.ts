import { Component, NgZone, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonContent, IonHeader, IonTitle, IonToolbar } from '@ionic/angular/standalone';
import { MedicalDeviceCommunicationService } from 'src/app/core/services/medical-device-communication.service';
import { DeviceDataManagerService } from 'src/app/core/services/device-data-manager.service';
import { NovaIcareLauncher } from 'src/app/core/plugin/nova-icare-launcher';
import { AlertController } from '@ionic/angular';
import { MatDialogRef } from '@angular/material/dialog';

import {
  DeviceType,
  PulseOximeterData,
  ThermometerData,
  BloodPressureData,
  ECGData,
  StethoscopeData,
  DeviceCommunicationResult,
  HemoglobinMeterData
} from 'src/app/core/interfaces/medical-device.interface';
import { Subscription } from 'rxjs';
import { App } from '@capacitor/app';

@Component({
  selector: 'app-hemoglobin',
  templateUrl: './hemoglobin.page.html',
  styleUrls: ['./hemoglobin.page.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule]
})
export class HemoglobinPage implements OnInit {
  resultdata: any = null;
  hemoglobin_data: HemoglobinMeterData | null = null;
  isReading: boolean = false;

  // Available devices tracking
  availableDevices: DeviceType[] = [];
  isLoadingDevices = true;

  private deviceDataSubscription?: Subscription;
  private appStateSubscription?: any;
  private centralizedSubscriptions: Subscription[] = [];

  constructor(
    private ngZone: NgZone,
    private medicalDeviceService: MedicalDeviceCommunicationService,
    private deviceDataManager: DeviceDataManagerService,
    private alertController: AlertController,
    private dialogRef: MatDialogRef<HemoglobinPage>
  ) { }

  async ngOnInit() {
    console.log('IcareDevicePage initialized');
    
    // Ensure isReading is false initially
    this.isReading = false;
    this.resultdata = null;

    // Load available devices first
    await this.loadAvailableDevices();

    // Register listeners for device results
    this.registerResultListener();
    this.registerMedicalDeviceListener();
    this.registerCentralizedDeviceListeners();

    // Register app state listener to re-register listeners when app becomes active
    this.appStateSubscription = App.addListener('appStateChange', ({ isActive }) => {
      console.log('App state changed. isActive:', isActive);
      if (isActive) {
        console.log('App is active again → re-registering listeners');
        this.registerResultListener();
        this.registerMedicalDeviceListener();
        this.registerCentralizedDeviceListeners();
      }
    });
  }
  closeDialog(): void {
    this.dialogRef.close();
  }

  registerMedicalDeviceListener() {
    try {
      // Subscribe to medical device communication service
      this.deviceDataSubscription = this.medicalDeviceService.getDeviceDataObservable()
        .subscribe({
          next: (result) => {
            console.log('MedicalDeviceService result received:', JSON.stringify(result, null, 2));
            console.log('DeviceType.HEMOGLOBIN enum value:', DeviceType.HEMOGLOBIN);
            console.log('result.deviceType value:', result.deviceType);
            console.log('result.deviceType.toLowerCase():', result.deviceType.toLowerCase());

            // Check with lowercase comparison since enum is 'hemoglobin'
            const isHemoglobinDevice = result.deviceType.toLowerCase() === DeviceType.HEMOGLOBIN;

            if (result.success && isHemoglobinDevice && result.data) {
              this.ngZone.run(() => {
                const hemoglobinData = result.data as HemoglobinMeterData;
                console.log('Hemoglobin data extracted:', JSON.stringify(hemoglobinData, null, 2));

                this.resultdata = {
                  hemoglobin_value: hemoglobinData.hemoglobin_value,
                  timestamp: result.timestamp,
                  source: 'MedicalDeviceService'
                };
                
                console.log('Setting isReading to false - data received');
                this.isReading = false;
                console.log('Result data updated from MedicalDeviceService:', JSON.stringify(this.resultdata, null, 2));
                console.log('Current isReading state:', this.isReading);
              });
            } else if (!result.success) {
              console.warn('Device result failed:', result.error);

              // Show user-friendly error message
              this.ngZone.run(() => {
                this.resultdata = {
                  error: result.error || 'Failed to get device result',
                  timestamp: Date.now(),
                  source: 'MedicalDeviceService'
                };
                console.log('Setting isReading to false - error received');
                this.isReading = false;
              });
            } else {
              console.log('Result not processed - success:', result.success, 'deviceType:', result.deviceType, 'isHemoglobinDevice:', isHemoglobinDevice, 'hasData:', !!result.data);
            }
          },
          error: (error) => {
            console.error('Error in medical device subscription:', error);

            this.ngZone.run(() => {
              this.resultdata = {
                error: 'Connection error with medical device service',
                timestamp: Date.now(),
                source: 'MedicalDeviceService'
              };
              console.log('Setting isReading to false - connection error');
              this.isReading = false;
            });
          }
        });

      console.log('MedicalDeviceService listener registered');
    } catch (error) {
      console.error('Error registering MedicalDeviceService listener:', error);
    }
  }

  registerResultListener() {
    try {
      NovaIcareLauncher.addListener('HemoglobinmeterResult', (data: any) => {
        console.log('NovaIcareLauncher HemoglobinmeterResult event received:', data);

        this.ngZone.run(() => {
          // Comprehensive null checks to prevent "Cannot read properties of undefined" errors
          if (!data) {
            console.warn('Received null or undefined data from NovaIcareLauncher');
            this.resultdata = {
              hemoglobin_value: '',
            };
            return;
          }

          const hasHemoglobinValue = data.hasOwnProperty('hemoglobin_value') && data.hemoglobin_value !== null && data.hemoglobin_value !== undefined;
          if (hasHemoglobinValue) {
            this.resultdata = {
              hemoglobin_value: data.hemoglobin_value,
              timestamp: Date.now(),
              source: 'NovaIcareLauncher'
            };
            this.isReading = false;
            console.log('Result data updated from NovaIcareLauncher:', this.resultdata);
          } else {
            console.warn('Invalid data structure received from NovaIcareLauncher:', this.stringifyData(data));
            this.resultdata = {
              hemoglobin_value: '',
            };
          }
        });
      });

      console.log('NovaIcareLauncher listener registered');
    } catch (error) {
      console.error('Error registering NovaIcareLauncher listener:', this.stringifyError(error));
    }
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK']
    });

    await alert.present();
  }

  /**
   * Register centralized device data listeners
   */
  registerCentralizedDeviceListeners() {
    try {
      console.log('🔗 Registering centralized device data listeners');

      // Subscribe to hemoglobin data
      const hemoglobinSub = this.deviceDataManager.getDeviceData<HemoglobinMeterData>(DeviceType.HEMOGLOBIN)
        .subscribe({
          next: (data) => {
            if (data) {
              this.ngZone.run(() => {
                this.resultdata = {
                  hemoglobin_value: data.hemoglobin_value,
                  source: 'CentralizedManager'
                };
                console.log('Hemoglobin data from centralized manager:', this.resultdata);
                this.isReading = false;
              });
            }
          },
          error: (error) => console.error('Error in hemoglobin subscription:', error)
        });

      // Store subscriptions for cleanup
      this.centralizedSubscriptions = [
        hemoglobinSub,
      ];

      console.log('Centralized device listeners registered successfully');
    } catch (error) {
      console.error('Error registering centralized device listeners:', error);
    }
  }

  async loadAvailableDevices() {
    try {
      console.log('Loading available devices...');
      this.isLoadingDevices = true;

      this.availableDevices = await this.deviceDataManager.getAvailableDevices();

      console.log('Available devices loaded:', this.availableDevices);
      this.isLoadingDevices = false;
    } catch (error) {
      console.error('Error loading available devices:', error);
      this.isLoadingDevices = false;

      // Fallback to showing all supported devices
      this.availableDevices = [
        DeviceType.HEMOGLOBIN,
      ];
    }
  }

  /**
   * Safely stringify data for logging
   */
  private stringifyData(data: any): string {
    try {
      return JSON.stringify(data);
    } catch {
      return String(data);
    }
  }

  /**
   * Safely stringify errors for logging
   */
  private stringifyError(error: any): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'object' && error !== null) {
      try {
        return JSON.stringify(error);
      } catch {
        return String(error);
      }
    }
    return String(error);
  }

  async launchIassist() {
    console.log('Launching iAssist - setting isReading to true');
    this.isReading = true; // Set loading state
    this.resultdata = null; // Clear previous data
    
    try {
      const res = await NovaIcareLauncher.launchDeviceWithResult({
        deviceType: DeviceType.HEMOGLOBIN,
        class_name: 'io.ionic.starter.MainActivity',
        package_name: 'io.ionic.starter',
        language: 'en',
        patient_id: '12345',
        real_id: '',
      });
      console.log('Launch success:', res);
    } catch (err) {
      console.error('Launch failed:', err);
      console.log('Launch failed - setting isReading to false');
      this.isReading = false;
    }
  }
}