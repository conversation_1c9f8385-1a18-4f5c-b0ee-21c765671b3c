<div id="ecg-dialog-wrapper">
    <div id="ecgHeader" class="ecg-header">
        <div class="ecg-header-row" id="ecgheading">

            <div class="battery" *ngIf="battery">
                <div class="flexCol">
                    <div class="battery-main">
                        <div class="batteryContainer">
                            <div class="battery-outer">
                                <div class="battery-level" [style.width.%]="battery" matTooltip="{{battery}}%"></div>
                            </div>
                            <div class="battery-tip"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ecg-title">Electrocardiogram</div>
            <button (click)="onDialogClose()" class="ecg-close-btn">✕</button>
        </div>

        <!-- ECG Content -->
        <div id="ecgContent">
            <div class="pulse-rate">Pulse Rate: {{ pulseData }}</div>
            <table *ngIf="ecgContents" id="ecgContents">
                <tr>
                    <td width="10%">x axis : <strong id="pulse_xaxis">25</strong> mm/sec</td>
                    <td width="10%">y axis : <strong id="pulse_yaxis">10</strong> mm/mv</td>
                    <td width="10%" style="color: #ff0000;">Gain Setting</td>
                    <td width="20%">
                        <select id="mmSelect" (change)="selectMMchange($event)">
          <option value="10">10mm/mv</option>
          <option value="5">5mm/mv</option>
          <option value="20">20mm/mv</option>
        </select>
                    </td>
                </tr>
            </table>

            <div style="text-align: center; font-size: 13px;">
                <p [innerHTML]="ecgDeviceErrorMessage" style="color: red;"></p>
            </div>

            <!-- ECG Canvas -->
            <div id="ecgcanvas-container" style="width: 1200px; height: 520px; margin: 10px;">
                <canvas id="ecg_canvas" width="1200" height="700"></canvas>
            </div>

            <div id="print" style="display: none;border:3px solid green;">
                <div style="display: flex; border:3px solid green;">
                    <table style="width: 50%;height:15vh;">
                        <tbody *ngFor="let details of allPatient;" style="font-size: 18px;">
                            <tr>
                                <td>Patient_Id</td>
                                <td>"details.p_id"</td>
                            </tr>
                            <tr>
                                <td>Patient_Name :</td>
                                <td>"details.p_name"</td>
                            </tr>
                            <tr>
                                <td>Patient_Age :</td>
                                <td>"details.p_age"</td>
                            </tr>
                            <tr>
                                <td>Gender :</td>
                                <td>"details.p_gender"</td>
                            </tr>

                        </tbody>
                    </table>
                    <table style="width: 50%;">
                        <tbody *ngFor="let details of allPatient;" style="font-size: 18px;">
                            <tr>

                            </tr>
                            <tr>

                            </tr>
                            <tr>

                                <td>25mm/sec</td>
                            </tr>
                            <tr>

                            </tr>
                            <tr>

                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div>

                <div style="position: relative;float: right;width: 350px;left: -21px;padding: 5px;" *ngIf="showPrintlive">
                    <a *ngIf="showButtonPrint" id="ecgprintbtn" class="btn btn-sm btn-success add-comment" style="padding: 4px 0px;margin-left: 204px;width: 23%;color: #fff; margin-top:200px;background-color: #037eb8;" (click)="EcgReport('$event')">Print</a>
                </div>
            </div>
        </div>