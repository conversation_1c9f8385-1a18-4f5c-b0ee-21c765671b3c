import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { PlatformType } from '../interfaces/medical-device.interface';

/**
 * Platform Detection Service
 * Enterprise-level service for detecting the current platform and capabilities
 */
@Injectable({
    providedIn: 'root'
})
export class PlatformDetectorService {
    private _currentPlatform: PlatformType;
    private _capabilities: PlatformCapabilities;

    constructor(private platform: Platform) {
        this._currentPlatform = this.detectPlatform();
        this._capabilities = this.detectCapabilities();

        console.log('🔍 Platform Detection:', {
            platform: this._currentPlatform,
            capabilities: this._capabilities
        });
    }

    /**
     * Get the current platform type
     */
    get currentPlatform(): PlatformType {
        return this._currentPlatform;
    }

    /**
     * Get platform capabilities
     */
    get capabilities(): PlatformCapabilities {
        return this._capabilities;
    }

    /**
     * Check if running on Android
     */
    get isAndroid(): boolean {
        return this._currentPlatform === PlatformType.ANDROID;
    }

    /**
     * Check if running on Web
     */
    get isWeb(): boolean {
        return this._currentPlatform === PlatformType.WEB;
    }

    /**
     * Check if running on Electron
     */
    get isElectron(): boolean {
        return this._currentPlatform === PlatformType.ELECTRON;
    }

    /**
     * Check if platform supports Android intents
     */
    get supportsAndroidIntents(): boolean {
        return this.isAndroid && this._capabilities.hasCapacitor;
    }

    /**
     * Check if platform supports WebSocket communication
     */
    get supportsWebSocket(): boolean {
        return this._capabilities.hasWebSocket;
    }

    /**
     * Check if platform supports Bluetooth
     */
    get supportsBluetooth(): boolean {
        return this._capabilities.hasBluetooth;
    }

    /**
     * Detect the current platform
     */
    private detectPlatform(): PlatformType {
        // Check for Electron first
        if (this.isElectronEnvironment()) {
            return PlatformType.ELECTRON;
        }

        // Enhanced Android detection - check multiple conditions
        if (this.isAndroidEnvironment()) {
            return PlatformType.ANDROID;
        }

        // Default to web
        return PlatformType.WEB;
    }

    /**
     * Enhanced Android environment detection
     */
    private isAndroidEnvironment(): boolean {
        // Check Ionic Platform detection
        if (this.platform.is('android')) {
            return true;
        }

        // Check if running in Capacitor on Android
        if (this.platform.is('capacitor') && this.platform.is('mobile')) {
            return true;
        }

        // Check Capacitor native context
        if (typeof window !== 'undefined' && (window as any).Capacitor) {
            const capacitor = (window as any).Capacitor;
            if (capacitor.getPlatform && capacitor.getPlatform() === 'android') {
                return true;
            }
            if (capacitor.platform === 'android') {
                return true;
            }
        }

        // Check user agent for Android
        if (typeof navigator !== 'undefined' && navigator.userAgent) {
            if (navigator.userAgent.toLowerCase().includes('android')) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if running in Electron environment
     */
    private isElectronEnvironment(): boolean {
        return !!(
            typeof window !== 'undefined' &&
            (window as any).electronAPI?.isElectron
        );
    }

    /**
     * Detect platform capabilities
     */
    private detectCapabilities(): PlatformCapabilities {
        const capabilities: PlatformCapabilities = {
            hasCapacitor: false,
            hasWebSocket: false,
            hasBluetooth: false,
            hasFileSystem: false,
            hasCamera: false,
            hasGeolocation: false,
            canLaunchExternalApps: false,
            supportsBackgroundTasks: false
        };

        // Check for Capacitor
        capabilities.hasCapacitor = !!(
            typeof window !== 'undefined' &&
            (window as any).Capacitor
        );

        // Check for WebSocket support
        capabilities.hasWebSocket = typeof WebSocket !== 'undefined';

        // Check for Bluetooth support
        capabilities.hasBluetooth = !!(
            typeof navigator !== 'undefined' &&
            (navigator as any).bluetooth
        );

        // Check for File System Access
        capabilities.hasFileSystem = !!(
            typeof window !== 'undefined' &&
            (window as any).showOpenFilePicker
        );

        // Check for Camera access
        capabilities.hasCamera = !!(
            typeof navigator !== 'undefined' &&
            navigator.mediaDevices &&
            navigator.mediaDevices.getUserMedia
        );

        // Check for Geolocation
        capabilities.hasGeolocation = !!(
            typeof navigator !== 'undefined' &&
            navigator.geolocation
        );

        // Platform-specific capabilities
        switch (this._currentPlatform) {
            case PlatformType.ANDROID:
                capabilities.canLaunchExternalApps = capabilities.hasCapacitor;
                capabilities.supportsBackgroundTasks = true;
                break;

            case PlatformType.ELECTRON:
                capabilities.canLaunchExternalApps = true;
                capabilities.supportsBackgroundTasks = true;
                capabilities.hasFileSystem = true;
                break;

            case PlatformType.WEB:
                capabilities.canLaunchExternalApps = false;
                capabilities.supportsBackgroundTasks = false;
                break;
        }

        return capabilities;
    }

    /**
     * Get platform-specific configuration
     */
    getPlatformConfig(): PlatformConfig {
        const baseConfig: PlatformConfig = {
            platform: this._currentPlatform,
            userAgent: navigator.userAgent,
            timestamp: Date.now()
        };

        switch (this._currentPlatform) {
            case PlatformType.ANDROID:
                return {
                    ...baseConfig,
                    androidVersion: this.getAndroidVersion(),
                    capacitorVersion: this.getCapacitorVersion(),
                    deviceModel: this.getDeviceModel()
                };

            case PlatformType.ELECTRON:
                return {
                    ...baseConfig,
                    electronVersion: this.getElectronVersion(),
                    nodeVersion: this.getNodeVersion(),
                    chromeVersion: this.getChromeVersion()
                };

            case PlatformType.WEB:
                return {
                    ...baseConfig,
                    browserName: this.getBrowserName(),
                    browserVersion: this.getBrowserVersion(),
                    isSecureContext: window.isSecureContext
                };

            default:
                return baseConfig;
        }
    }

    /**
     * Helper methods for version detection
     */
    private getAndroidVersion(): string | undefined {
        const match = navigator.userAgent.match(/Android (\d+(?:\.\d+)*)/);
        return match ? match[1] : undefined;
    }

    private getCapacitorVersion(): string | undefined {
        return (window as any).Capacitor?.version;
    }

    private getDeviceModel(): string | undefined {
        // This would typically come from a Capacitor plugin
        return undefined;
    }

    private getElectronVersion(): string | undefined {
        return (window as any).electronAPI?.version;
    }

    private getNodeVersion(): string | undefined {
        return (window as any).process?.versions?.node;
    }

    private getChromeVersion(): string | undefined {
        const match = navigator.userAgent.match(/Chrome\/(\d+(?:\.\d+)*)/);
        return match ? match[1] : undefined;
    }

    private getBrowserName(): string {
        const userAgent = navigator.userAgent;
        if (userAgent.includes('Firefox')) return 'Firefox';
        if (userAgent.includes('Chrome')) return 'Chrome';
        if (userAgent.includes('Safari')) return 'Safari';
        if (userAgent.includes('Edge')) return 'Edge';
        return 'Unknown';
    }

    private getBrowserVersion(): string | undefined {
        const userAgent = navigator.userAgent;
        const browserName = this.getBrowserName();

        const patterns: { [key: string]: RegExp } = {
            'Firefox': /Firefox\/(\d+(?:\.\d+)*)/,
            'Chrome': /Chrome\/(\d+(?:\.\d+)*)/,
            'Safari': /Version\/(\d+(?:\.\d+)*)/,
            'Edge': /Edge\/(\d+(?:\.\d+)*)/
        };

        const pattern = patterns[browserName];
        if (pattern) {
            const match = userAgent.match(pattern);
            return match ? match[1] : undefined;
        }

        return undefined;
    }
}

/**
 * Platform capabilities interface
 */
export interface PlatformCapabilities {
    hasCapacitor: boolean;
    hasWebSocket: boolean;
    hasBluetooth: boolean;
    hasFileSystem: boolean;
    hasCamera: boolean;
    hasGeolocation: boolean;
    canLaunchExternalApps: boolean;
    supportsBackgroundTasks: boolean;
}

/**
 * Platform configuration interface
 */
export interface PlatformConfig {
    platform: PlatformType;
    userAgent: string;
    timestamp: number;
    androidVersion?: string;
    capacitorVersion?: string;
    deviceModel?: string;
    electronVersion?: string;
    nodeVersion?: string;
    chromeVersion?: string;
    browserName?: string;
    browserVersion?: string;
    isSecureContext?: boolean;
}
