// Enhanced Results Container
.results-container {
  margin-top: 20px;
  padding: 0 16px;
}

// Beautiful Results Card with Gradient
.results-card {
  background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
  border-radius: 16px;
  padding: 24px;
  margin: 16px 0;
  box-shadow: 0 8px 32px rgba(63, 81, 181, 0.15);
  border: 1px solid rgba(63, 81, 181, 0.1);
  position: relative;
  overflow: hidden;

  // Decorative accent
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3f51b5, #2196f3, #00bcd4);
  }

  h2 {
    margin: 0 0 24px 0;
    color: #3f51b5;
    text-align: center;
    font-size: 26px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(63, 81, 181, 0.1);
  }

  h3 {
    margin: 24px 0 16px 0;
    color: #2196f3;
    text-align: center;
    font-size: 20px;
    font-weight: 600;
  }
}

// Test Information Section with Colors
.test-info {
  margin-bottom: 28px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 2px solid rgba(63, 81, 181, 0.1);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(63, 81, 181, 0.05);
      border-radius: 8px;
      padding: 16px 12px;
      transform: translateY(-1px);
    }

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-weight: 700;
      color: #1a237e;
      flex: 0 0 auto;
      margin-right: 16px;
      font-size: 16px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .value {
      color: #424242;
      text-align: right;
      flex: 1;
      word-break: break-word;
      font-size: 15px;
      font-weight: 500;
      
      // Different colors for different types of values
      &.positive {
        color: #2e7d32;
        font-weight: 600;
      }
      
      &.negative {
        color: #d32f2f;
        font-weight: 600;
      }
      
      &.neutral {
        color: #f57c00;
        font-weight: 600;
      }
    }
  }
}

// Enhanced Image Section
.image-section {
  text-align: center;
  margin-top: 24px;
  
  h3 {
    color: #3f51b5;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
  }
}

.image-container {
  margin: 20px 0;
  text-align: center;
  background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.test-image {
  max-width: 100%;
  max-height: 350px;
  width: auto;
  height: auto;
  border: 3px solid #3f51b5;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(63, 81, 181, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.02);
    box-shadow: 0 12px 32px rgba(63, 81, 181, 0.4);
  }
}

// Action Buttons with Color
.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 20px;
  flex-wrap: wrap;

  ion-button {
    --background: linear-gradient(135deg, #3f51b5 0%, #2196f3 100%);
    --background-hover: linear-gradient(135deg, #303f9f 0%, #1976d2 100%);
    --color: white;
    --border-radius: 12px;
    --padding-start: 24px;
    --padding-end: 24px;
    --box-shadow: 0 4px 12px rgba(63, 81, 181, 0.3);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &:hover {
      transform: translateY(-2px);
      --box-shadow: 0 6px 16px rgba(63, 81, 181, 0.4);
    }
  }
}

// No Image State with Style
.no-image {
  text-align: center;
  padding: 48px 24px;
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  border-radius: 12px;
  border: 2px dashed #bdbdbd;
  margin: 20px 0;

  p {
    margin: 0;
    font-size: 18px;
    color: #757575;
    font-weight: 500;
  }

  .icon {
    font-size: 48px;
    color: #bdbdbd;
    margin-bottom: 16px;
  }
}

// Dropdown/Select Enhancement
ion-select {
  --background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  --color: #3f51b5;
  --border-color: #3f51b5;
  --border-width: 2px;
  --border-radius: 12px;
  --padding: 16px;
  font-weight: 600;
}

ion-item {
  --background: transparent;
  --border-color: rgba(63, 81, 181, 0.2);
  --inner-border-width: 0 0 2px 0;
  
  ion-label {
    color: #3f51b5;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

// Launch Button Enhancement
.launch-button {
  --background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
  --background-hover: linear-gradient(135deg, #388e3c 0%, #1b5e20 100%);
  --color: white;
  --border-radius: 16px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  --box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
  font-size: 18px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 24px 0;

  &:hover {
    transform: translateY(-3px);
    --box-shadow: 0 8px 28px rgba(76, 175, 80, 0.4);
  }

  &:disabled {
    --background: #e0e0e0;
    --color: #9e9e9e;
    --box-shadow: none;
  }
}

// Selected Test Display
.selected-test {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%);
  border: 2px solid #4caf50;
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
  text-align: center;

  p {
    margin: 0;
    color: #2e7d32;
    font-weight: 600;
    font-size: 16px;
  }

  strong {
    color: #1b5e20;
    font-size: 18px;
  }
}

// Loading Animation
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e3f2fd;
    border-top: 4px solid #3f51b5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive Design with Colors
@media (max-width: 768px) {
  .results-card {
    margin: 12px 8px;
    padding: 20px;
    border-radius: 12px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 12px 0;

    .value {
      text-align: left;
      margin-top: 8px;
      width: 100%;
    }
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;

    ion-button {
      width: 100%;
      max-width: 280px;
    }
  }

  .test-image {
    max-height: 250px;
  }
}

// Dark Mode Support
@media (prefers-color-scheme: dark) {
  .results-card {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border: 1px solid rgba(159, 168, 218, 0.2);
    
    h2 {
      color: #9fa8da;
    }
    
    h3 {
      color: #81c784;
    }
  }

  .test-info {
    background: rgba(255, 255, 255, 0.05);
    
    .info-item {
      border-bottom: 2px solid rgba(159, 168, 218, 0.1);
      
      .label {
        color: #c5cae9;
      }
      
      .value {
        color: #e1e1e1;
      }
    }
  }
  
  .no-image {
    background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
    border-color: #555;
    
    p {
      color: #bbb;
    }
  }
}