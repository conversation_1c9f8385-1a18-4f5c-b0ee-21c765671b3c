import { registerPlugin } from '@capacitor/core';
import { PulseOximeterData, ThermometerData, BloodPressureData, ECGData, StethoscopeData, DeviceType,OpticalReaderData } from '../interfaces/medical-device.interface';
import { HemoglobinMeterData } from '../interfaces/medical-device.interface';

// export interface NovaIcareData {
//   spo2: string;
//   pulse_rate: string;
// }

export interface NovaIcareOptions {
  class_name: string;
  package_name: string;
  language: string;
  patient_id: string;
  real_id?: string;
  deviceType?: DeviceType;
  testType?: string;
}

// export interface DeviceLaunchOptions {
//   deviceType: DeviceType;
//   packageName: string;
//   className: string;
//   patientId: string;
//   language?: string;
//   realId?: string;
// }

const NovaIcareLauncher = registerPlugin<{
  launchDeviceWithResult(options: NovaIcareOptions & { deviceType: DeviceType }): Promise<any>;
  
} & {
  addListener(eventName: 'PulseOximeterResult', listenerFunc: (data: PulseOximeterData) => void): any;
  addListener(eventName: 'ThermometerResult', listenerFunc: (data: ThermometerData) => void): any;
  addListener(eventName: 'BloodPressureResult', listenerFunc: (data: BloodPressureData) => void): any;
  addListener(eventName: 'ECGResult', listenerFunc: (data: ECGData) => void): any;
  addListener(eventName: 'StethoscopeResult', listenerFunc: (data: StethoscopeData) => void): any;
  addListener(eventName: 'DeviceResult', listenerFunc: (data: any) => void): any;
  addListener(eventName: 'opticalreaderlis', listenerFunc: (data: OpticalReaderData) => void): any;
  addListener(eventName: 'HemoglobinmeterResult', listenerFunc: (data: HemoglobinMeterData) => void): any;
}>('NovaIcareLauncherPlugin');

/**
 * Helper functions for migration
 */
/** * Convert legacy options to modern format */
// export const NovaIcareHelper = {
//   convertToModernOptions(legacyOptions: NovaIcareOptions): DeviceLaunchOptions {
//     return {
//       deviceType: legacyOptions.deviceType || DeviceType.PULSE_OXIMETER,
//       packageName: legacyOptions.package_name,
//       className: legacyOptions.class_name,
//       patientId: legacyOptions.patient_id,
//       language: legacyOptions.language,
//       realId: legacyOptions.real_id
//     };
//   },

/**
 * Launch device with modern options format
 */
// async launchDevice(deviceType: DeviceType, patientId: string, options?: Partial<DeviceLaunchOptions>): Promise<any> {
//   console.warn('⚠️ NovaIcareLauncher is deprecated. Please migrate to MedicalDeviceCommunicationPlugin');

//   return NovaIcareLauncher.launchDeviceWithResult({
//     deviceType,
//     class_name: options?.className || 'io.ionic.starter.MainActivity',
//     package_name: options?.packageName || 'io.ionic.starter',
//     language: options?.language || 'en',
//     patient_id: patientId,
//     real_id: options?.realId || ''
//   });
// },

/**
 * Launch specific device types with proper parameter mapping
 */
// async launchPulseOximeter(patientId: string, options?: Partial<DeviceLaunchOptions>): Promise<any> {
//   return this.launchDevice(DeviceType.PULSE_OXIMETER, patientId, options);
// },

// async launchThermometer(patientId: string, options?: Partial<DeviceLaunchOptions>): Promise<any> {
//   return this.launchDevice(DeviceType.THERMOMETER, patientId, options);
// },

// async launchBloodPressure(patientId: string, options?: Partial<DeviceLaunchOptions>): Promise<any> {
//   return this.launchDevice(DeviceType.BLOOD_PRESSURE, patientId, options);
// },

// async launchECG(patientId: string, options?: Partial<DeviceLaunchOptions>): Promise<any> {
//   return this.launchDevice(DeviceType.ECG, patientId, options);
// },

// async launchStethoscope(patientId: string, options?: Partial<DeviceLaunchOptions>): Promise<any> {
//   return this.launchDevice(DeviceType.STETHOSCOPE, patientId, options);
// }
// };

export { NovaIcareLauncher };
