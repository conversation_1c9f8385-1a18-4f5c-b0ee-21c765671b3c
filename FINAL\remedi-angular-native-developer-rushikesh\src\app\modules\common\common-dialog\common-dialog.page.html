<div class="dialog-container">
    <h2>{{ data.title }}</h2>
    <p>{{ data.message }}</p>

    <mat-form-field appearance="outline" class="dialog-input">
        <mat-label>Enter something</mat-label>
        <input matInput [(ngModel)]="userInput" placeholder="Type here..." />
    </mat-form-field>

    <div class="dialog-actions">
        <button mat-raised-button color="primary" (click)="confirm()">OK</button>
        <button mat-stroked-button color="warn" (click)="cancel()">Cancel</button>
    </div>
</div>