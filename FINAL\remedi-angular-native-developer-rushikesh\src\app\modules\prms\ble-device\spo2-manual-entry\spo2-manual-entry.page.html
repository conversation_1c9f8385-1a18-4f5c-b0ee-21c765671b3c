<div class="hemoglobin-dialog-container">
    <!-- Header with Title and Close Button -->
    <div class="spo2-header">
        <div style="flex: 1; text-align: center; font-weight: bold; font-size: 18px;">
            Hemoglobin
        </div>
        <button class="spo2-close-btn" (click)="onDialogClose()" title="Close">
      ✕
    </button>
    </div>

    <!-- Body -->
    <div class="modal-body">
        <div *ngIf="showBatchCodeUI" class="instruction-text">
            <ol>
                <li>Enter the 3-digit batch code from the strip bottle.</li>
                <li>Ensure the same code is entered on the device.</li>
            </ol>
            <input type="text" [(ngModel)]="batchCode" maxlength="3" placeholder="Enter batch code" />
            <div class="center">
                <button class="take-reading-btn" (click)="takeReading()">Take Reading</button>
            </div>
        </div>

        <!-- Success/Error message -->
        <div *ngIf="resultMessage" class="status-message" [ngClass]="{'success-msg': isSuccess, 'error-msg': !isSuccess}">
            {{ resultMessage }}
        </div>

        <hr />

        <div class="manual-entry">
            <b>Manual Entry</b>
            <div class="entry-group">
                <label>Enter Hemoglobin Result</label>
                <input type="number" [(ngModel)]="manualResult" step="0.1" placeholder="Enter the result" />
                <span>g/dl</span>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="modal-footer">
        <!-- <button class="btn btn-primary save-btn" (click)="save()">Save</button> -->
    </div>
</div>