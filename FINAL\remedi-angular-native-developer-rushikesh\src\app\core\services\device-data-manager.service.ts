import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { BehaviorSubject, Subject, Observable, merge } from 'rxjs';
import { takeUntil, filter, map, distinctUntilChanged } from 'rxjs/operators';
import {
    DeviceType,
    DeviceCommunicationResult,
    PulseOximeterData,
    StethoscopeData,
    ThermometerData,
    BloodPressureData,
    ECGData,
    DeviceStatus,
    PlatformType
} from '../interfaces/medical-device.interface';
import { MedicalDeviceCommunicationService } from './medical-device-communication.service';
import { WebsocketsService } from './websocket.service';

/**
 * Centralized Device Data Manager Service
 * Single point of truth for all medical device data across the application
 */
@Injectable({
    providedIn: 'root'
})
export class DeviceDataManagerService implements OnDestroy {

    private destroy$ = new Subject<void>();

    // Centralized data streams for each device type
    private pulseOximeterData$ = new BehaviorSubject<PulseOximeterData | null>(null);
    private stethoscopeData$ = new BehaviorSubject<StethoscopeData | null>(null);
    private thermometerData$ = new BehaviorSubject<ThermometerData | null>(null);
    private bloodPressureData$ = new BehaviorSubject<BloodPressureData | null>(null);
    private ecgData$ = new BehaviorSubject<ECGData | null>(null);

    // Device status tracking
    private deviceStatus$ = new BehaviorSubject<Map<DeviceType, DeviceStatus>>(new Map());

    // All device data combined stream
    private allDeviceData$ = new BehaviorSubject<DeviceCommunicationResult | null>(null);

    // Device connection status
    private connectionStatus$ = new BehaviorSubject<Map<DeviceType, boolean>>(new Map());

    // Error tracking
    private deviceErrors$ = new BehaviorSubject<Map<DeviceType, string>>(new Map());

    constructor(
        private medicalDeviceService: MedicalDeviceCommunicationService,
        private websocketsService: WebsocketsService
    ) {
        this.initializeDataStreams();
    }

    /**
     * Initialize all data streams and listeners
     */
    private initializeDataStreams(): void {
        console.log('🚀 Initializing Centralized Device Data Manager');

        // Subscribe to medical device communication service
        this.medicalDeviceService.getDeviceDataObservable()
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (result) => this.handleDeviceResult(result),
                error: (error) => this.handleError('MedicalDeviceService', error)
            });

        // Subscribe to WebSocket service for legacy support
        this.websocketsService.bleValueObs$
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (data) => this.handleWebSocketData(data),
                error: (error) => this.handleError('WebSocketService', error)
            });

        // Subscribe to connection status updates
        this.medicalDeviceService.getConnectionStatusObservable()
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (statusMap) => this.updateConnectionStatus(statusMap),
                error: (error) => this.handleError('ConnectionStatus', error)
            });

        console.log('✅ Device Data Manager initialized successfully');
    }

    /**
     * Handle device communication results
     */
    private handleDeviceResult(result: DeviceCommunicationResult): void {
        try {
            console.log('📥 Processing device result:', result);

            // Update all device data stream
            this.allDeviceData$.next(result);

            // Route data to specific device streams
            if (result.success && result.data) {
                this.routeDeviceData(result.deviceType, result.data);
            } else if (!result.success) {
                this.handleDeviceError(result.deviceType, result.error || 'Unknown error');
            }

            // Update connection status
            this.updateDeviceConnectionStatus(result.deviceType, result.success);

        } catch (error) {
            console.error('❌ Error handling device result:', error);
            this.handleError(`DeviceResult-${result.deviceType}`, error);
        }
    }

    /**
     * Route data to specific device type streams
     * Each device returns its own specific data format from NovaICare
     */
    private routeDeviceData(deviceType: DeviceType, data: any): void {
        console.log(`📊 Routing data for ${deviceType}:`, data);

        switch (deviceType) {
            case DeviceType.PULSE_OXIMETER:
                this.pulseOximeterData$.next(this.transformToPulseOximeterData(data));
                break;
            case DeviceType.STETHOSCOPE:
                this.stethoscopeData$.next(this.transformToStethoscopeData(data));
                break;
            case DeviceType.THERMOMETER:
                this.thermometerData$.next(this.transformToThermometerData(data));
                break;
            case DeviceType.BLOOD_PRESSURE:
                this.bloodPressureData$.next(this.transformToBloodPressureData(data));
                break;
            case DeviceType.ECG:
                this.ecgData$.next(this.transformToECGData(data));
                break;
            default:
                console.warn('⚠️ Unknown device type:', deviceType);
        }
    }

    /**
     * Transform raw data to PulseOximeterData format
     */
    private transformToPulseOximeterData(data: any): PulseOximeterData {
        return {
            spo2: this.safeParseNumber(data?.spo2, 0),
            pulseRate: this.safeParseNumber(data?.pulse_rate, 0),
            timestamp: Date.now(),
            batteryLevel: data?.batteryLevel,
            signalQuality: data?.signalQuality
        };
    }

    /**
     * Transform raw data to StethoscopeData format
     * Uses actual stethoscope data from NovaICare StethoSensor activity
     */
    private transformToStethoscopeData(data: any): StethoscopeData {
        if (!data) {
            console.warn('⚠️ No stethoscope data received');
            throw new Error('No stethoscope data received from device');
        }

        const fileName = data?.Stetho_Reading || data?.file_name;
        const value = data?.stetho_value;
        const heartRate = data?.heart_rate ? this.safeParseNumber(data.heart_rate, 0) : undefined;

        if (!fileName && !value) {
            throw new Error('Invalid stethoscope data received from device');
        }

        return {
            audioData: new ArrayBuffer(1024), // Audio file would be loaded from fileName
            duration: 30, // Default duration
            sampleRate: 44100,
            timestamp: Date.now(),
            heartRate: heartRate,
            fileName: fileName,
            value: value
        };
    }

    /**
     * Transform raw data to ThermometerData format
     * Uses actual temperature data from NovaICare TemperatureSensor1 activity
     */
    private transformToThermometerData(data: any): ThermometerData {
        if (!data) {
            console.warn('⚠️ No thermometer data received');
            throw new Error('No thermometer data received from device');
        }

        const celcius = this.safeParseNumber(data?.celcius, 0);
        const fahrenheit = this.safeParseNumber(data?.fahrenheit, 0);

        // Use celsius if available, otherwise convert from fahrenheit
        let temperature = celcius;
        let unit: 'celsius' | 'fahrenheit' = 'celsius';

        if (celcius === 0 && fahrenheit > 0) {
            temperature = (fahrenheit - 32) * 5 / 9;
            unit = 'celsius';
        }

        if (temperature === 0) {
            throw new Error('Invalid temperature data received from device');
        }

     return {
  temperature: Math.round(temperature * 10) / 10,
  unit: unit,
  timestamp: Date.now(),
  batteryLevel: data?.batteryLevel,
//   sensorType: data?.sensor_type,
  source: 'WebSocketDeviceService' // ✅ Add this line with a relevant source name
};

    }

    /**
     * Transform raw data to BloodPressureData format
     * Uses actual blood pressure data from NovaICare BloodPressureSensor1 activity
     */
    private transformToBloodPressureData(data: any): BloodPressureData {
        if (!data) {
            console.warn('⚠️ No blood pressure data received');
            throw new Error('No blood pressure data received from device');
        }

        const systolic = this.safeParseNumber(data?.systolic, 0);
        const diastolic = this.safeParseNumber(data?.diastolic, 0);
        const pulseRate = this.safeParseNumber(data?.pulse_rate, 0);

        if (systolic === 0 || diastolic === 0) {
            throw new Error('Invalid blood pressure data received from device');
        }

        return {
            systolic: systolic,
            diastolic: diastolic,
            pulse: pulseRate,
            timestamp: Date.now(),
            batteryLevel: data?.batteryLevel,
            sensorType: data?.sensor_type,
            error: data?.error
        };
    }

    /**
     * Transform raw data to ECGData format
     * Uses actual ECG data from NovaICare ECGSensor1 activity
     */
    private transformToECGData(data: any): ECGData {
        if (!data) {
            console.warn('⚠️ No ECG data received');
            throw new Error('No ECG data received from device');
        }

        const pulseRate = this.safeParseNumber(data?.pulse_rate, 0);
        const fileName = data?.file_name;
        const value = data?.ecg_value;

        // Extract 12-lead ECG data
        const leads = [];
        const waveformData: number[] = [];

        if (data?.ecg_lead1) leads.push('Lead I');
        if (data?.ecg_lead2) leads.push('Lead II');
        if (data?.ecg_lead3) leads.push('Lead III');
        if (data?.ecg_avr) leads.push('aVR');
        if (data?.ecg_avl) leads.push('aVL');
        if (data?.ecg_avf) leads.push('aVF');
        if (data?.ecg_v1) leads.push('V1');
        if (data?.ecg_v2) leads.push('V2');
        if (data?.ecg_v3) leads.push('V3');
        if (data?.ecg_v4) leads.push('V4');
        if (data?.ecg_v5) leads.push('V5');
        if (data?.ecg_v6) leads.push('V6');

        if (leads.length === 0) {
            throw new Error('No ECG lead data received from device');
        }

        // Convert lead data to waveform (simplified - actual implementation would parse the lead data)
        for (let i = 0; i < 1000; i++) {
            waveformData.push(Math.sin(i * 0.1) * 100 + Math.random() * 10);
        }

        return {
            waveformData,
            heartRate: pulseRate,
            duration: 10, // Default duration
            timestamp: Date.now(),
            leads: leads,
            fileName: fileName,
            value: value,
            sensorType: data?.sensor_type,
            leadData: {
                lead1: data?.ecg_lead1,
                lead2: data?.ecg_lead2,
                lead3: data?.ecg_lead3,
                avr: data?.ecg_avr,
                avl: data?.ecg_avl,
                avf: data?.ecg_avf,
                v1: data?.ecg_v1,
                v2: data?.ecg_v2,
                v3: data?.ecg_v3,
                v4: data?.ecg_v4,
                v5: data?.ecg_v5,
                v6: data?.ecg_v6
            }
        };
    }

    /**
     * Handle WebSocket data for legacy support
     */
    private handleWebSocketData(data: any): void {
        try {
            if (!data) return;

            const dataStr = typeof data === 'string' ? data : String(data);
            console.log('📡 Processing WebSocket data:', dataStr);

            // Parse different WebSocket message types
            if (dataStr.includes('plotSpo2Graph')) {
                const splitData = dataStr.split('~');
                if (splitData.length > 1) {
                    const spo2Data: Partial<PulseOximeterData> = {
                        spo2: 0, // Will be updated by setCurrentSpo2
                        pulseRate: 0, // Will be updated by setCurrentSpo2
                        timestamp: Date.now()
                    };
                    this.pulseOximeterData$.next(spo2Data as PulseOximeterData);
                }
            }

            if (dataStr.includes('setCurrentSpo2')) {
                const splitData = dataStr.split('~');
                if (splitData.length > 1) {
                    const currentData = this.pulseOximeterData$.value || { spo2: 0, pulseRate: 0, timestamp: Date.now() };
                    const updatedData: PulseOximeterData = {
                        ...currentData,
                        spo2: this.safeParseNumber(splitData[1], 0),
                        timestamp: Date.now()
                    };
                    this.pulseOximeterData$.next(updatedData);
                }
            }

            if (dataStr.includes('setTemperature')) {
                const splitData = dataStr.split('~');
                if (splitData.length > 1) {
                    const tempF = this.safeParseNumber(splitData[1], 0);
                    const tempC = (tempF - 32) * 5 / 9;
                    const thermometerData: ThermometerData = {
                        temperature: Math.round(tempC * 100) / 100,
                        unit: 'celsius',
                        timestamp: Date.now(),
                        source: 'WebSocketDeviceService' // ✅ Add this line
                    };

                    this.thermometerData$.next(thermometerData);
                }
            }

            if (dataStr.includes('changeBatteryStatus')) {
                const splitData = dataStr.split('~');
                if (splitData.length > 1) {
                    const batteryBit = this.safeParseNumber(splitData[1], 0);
                    let batteryLevel = 0;
                    if (batteryBit < 4) batteryLevel = 10;
                    else if (batteryBit === 4) batteryLevel = 33;
                    else if (batteryBit === 5) batteryLevel = 66;
                    else if (batteryBit === 6) batteryLevel = 100;

                    // Update current pulse oximeter data with battery info
                    const currentData = this.pulseOximeterData$.value;
                    if (currentData) {
                        this.pulseOximeterData$.next({
                            ...currentData,
                            batteryLevel,
                            timestamp: Date.now()
                        });
                    }
                }
            }

        } catch (error) {
            console.error('❌ Error processing WebSocket data:', error);
            this.handleError('WebSocketData', error);
        }
    }

    /**
     * Handle device-specific errors
     */
    private handleDeviceError(deviceType: DeviceType, error: string): void {
        const currentErrors = this.deviceErrors$.value;
        const updatedErrors = new Map(currentErrors);
        updatedErrors.set(deviceType, error);
        this.deviceErrors$.next(updatedErrors);

        console.error(`❌ Device error for ${deviceType}:`, error);
    }

    /**
     * Handle general errors
     */
    private handleError(source: string, error: any): void {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`❌ Error in ${source}:`, errorMessage);
    }

    /**
     * Update connection status for a specific device
     */
    private updateDeviceConnectionStatus(deviceType: DeviceType, isConnected: boolean): void {
        const currentStatus = this.connectionStatus$.value;
        const updatedStatus = new Map(currentStatus);
        updatedStatus.set(deviceType, isConnected);
        this.connectionStatus$.next(updatedStatus);
    }

    /**
     * Update connection status from service
     */
    private updateConnectionStatus(statusMap: Map<DeviceType, any>): void {
        const connectionMap = new Map<DeviceType, boolean>();
        statusMap.forEach((status, deviceType) => {
            const isConnected = typeof status === 'boolean' ? status : status === 'connected';
            connectionMap.set(deviceType, isConnected);
        });
        this.connectionStatus$.next(connectionMap);
    }

    /**
     * Safely parse numeric values
     */
    private safeParseNumber(value: any, fallback: number): number {
        if (value === null || value === undefined || value === '') {
            return fallback;
        }
        const parsed = typeof value === 'string' ? parseFloat(value) : Number(value);
        return isNaN(parsed) ? fallback : parsed;
    }

    // PUBLIC API METHODS

    /**
     * Get data observable for specific device type
     */
    getDeviceData<T>(deviceType: DeviceType): Observable<T | null> {
        switch (deviceType) {
            case DeviceType.PULSE_OXIMETER:
                return this.pulseOximeterData$.asObservable() as Observable<T | null>;
            case DeviceType.STETHOSCOPE:
                return this.stethoscopeData$.asObservable() as Observable<T | null>;
            case DeviceType.THERMOMETER:
                return this.thermometerData$.asObservable() as Observable<T | null>;
            case DeviceType.BLOOD_PRESSURE:
                return this.bloodPressureData$.asObservable() as Observable<T | null>;
            case DeviceType.ECG:
                return this.ecgData$.asObservable() as Observable<T | null>;
            default:
                throw new Error(`Unsupported device type: ${deviceType}`);
        }
    }

    /**
     * Get all device data stream
     */
    getAllDeviceData(): Observable<DeviceCommunicationResult | null> {
        return this.allDeviceData$.asObservable();
    }

    /**
     * Get connection status for all devices
     */
    getConnectionStatus(): Observable<Map<DeviceType, boolean>> {
        return this.connectionStatus$.asObservable();
    }

    /**
     * Get connection status for specific device
     */
    getDeviceConnectionStatus(deviceType: DeviceType): Observable<boolean> {
        return this.connectionStatus$.pipe(
            map(statusMap => statusMap.get(deviceType) || false),
            distinctUntilChanged()
        );
    }

    /**
     * Get errors for all devices
     */
    getDeviceErrors(): Observable<Map<DeviceType, string>> {
        return this.deviceErrors$.asObservable();
    }

    /**
     * Get error for specific device
     */
    getDeviceError(deviceType: DeviceType): Observable<string | null> {
        return this.deviceErrors$.pipe(
            map(errorMap => errorMap.get(deviceType) || null),
            distinctUntilChanged()
        );
    }

    /**
     * Launch device through centralized manager
     * Uses proper MedicalDeviceCommunicationService which calls correct device-specific activities
     */
    async launchDevice(deviceType: DeviceType, patientId: string, additionalParams?: any): Promise<DeviceCommunicationResult> {
        try {
            console.log(`🚀 Launching ${deviceType} through centralized manager (via MedicalDeviceService)`);

            // Use the medical device service for all platforms - it handles device-specific activities correctly
            const result = await this.medicalDeviceService.launchDevice({
                deviceType,
                patientId,
                realId: additionalParams?.realId,
                language: additionalParams?.language || 'en',
                sessionId: additionalParams?.sessionId,
                additionalParams
            });

            console.log(`✅ Device launch result for ${deviceType}:`, result);

            // The result should already be processed by the medical device service
            // but we can also handle it here for additional processing if needed
            this.handleDeviceResult(result);

            return result;

        } catch (error) {
            console.error(`❌ Error launching ${deviceType}:`, error);
            const errorResult: DeviceCommunicationResult = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                platform: this.getCurrentPlatform(),
                deviceType,
                timestamp: Date.now()
            };
            this.handleDeviceResult(errorResult);
            return errorResult;
        }
    }

    /**
     * Check if device is supported
     */
    isDeviceSupported(deviceType: DeviceType): boolean {
        return this.medicalDeviceService.isDeviceSupported(deviceType);
    }

    /**
     * Get supported devices
     */
    getSupportedDevices(): DeviceType[] {
        return this.medicalDeviceService.getSupportedDevices();
    }

    /**
     * Check if device is actually available (installed and working)
     * Since NovaICare only has PulseOxiMeterSensor1 activity, all devices use the same activity
     */
    async isDeviceAvailable(deviceType: DeviceType): Promise<boolean> {
        try {
            if (this.getCurrentPlatform() === PlatformType.ANDROID) {
                // All devices use the same NovaICare activity (PulseOxiMeterSensor1)
                // So if NovaICare is installed, all device types are "available"
                if (typeof window !== 'undefined' && (window as any).Capacitor) {
                    try {
                        const { NovaIcareLauncher } = await import('../plugin/nova-icare-launcher');
                        // If we can import the plugin, assume NovaICare is available
                        return true;
                    } catch (error) {
                        console.error('❌ NovaIcareLauncher not available:', error);
                        return false;
                    }
                }
            }

            // For other platforms, assume supported devices are available
            return this.isDeviceSupported(deviceType);
        } catch (error) {
            console.error(`❌ Error checking device availability for ${deviceType}:`, error);
            return false;
        }
    }

    /**
     * Get list of actually available devices
     * Since NovaICare uses one activity for all devices, all are available if the app is installed
     */
    async getAvailableDevices(): Promise<DeviceType[]> {
        try {
            if (this.getCurrentPlatform() === PlatformType.ANDROID) {
                // Check if NovaICare is available by checking one device type
                const isNovaICareAvailable = await this.isDeviceAvailable(DeviceType.PULSE_OXIMETER);

                if (isNovaICareAvailable) {
                    // All device types are available since they use the same activity
                    const availableDevices = [
                        DeviceType.PULSE_OXIMETER,
                        DeviceType.STETHOSCOPE,
                        DeviceType.THERMOMETER,
                        DeviceType.BLOOD_PRESSURE,
                        DeviceType.ECG
                    ];
                    console.log('📱 All devices available via NovaICare:', availableDevices);
                    return availableDevices;
                } else {
                    console.log('📱 NovaICare not available, no devices available');
                    return [];
                }
            } else {
                // For other platforms, return all supported devices
                const supportedDevices = [
                    DeviceType.PULSE_OXIMETER,
                    DeviceType.STETHOSCOPE,
                    DeviceType.THERMOMETER,
                    DeviceType.BLOOD_PRESSURE,
                    DeviceType.ECG
                ];
                console.log('📱 All devices available on non-Android platform:', supportedDevices);
                return supportedDevices;
            }
        } catch (error) {
            console.error('❌ Error getting available devices:', error);
            return [DeviceType.PULSE_OXIMETER, DeviceType.STETHOSCOPE]; // Fallback
        }
    }

    /**
     * Get current platform
     */
    getCurrentPlatform(): PlatformType {
        return this.medicalDeviceService.getCurrentPlatform();
    }

    /**
     * Clear data for specific device
     */
    clearDeviceData(deviceType: DeviceType): void {
        switch (deviceType) {
            case DeviceType.PULSE_OXIMETER:
                this.pulseOximeterData$.next(null);
                break;
            case DeviceType.STETHOSCOPE:
                this.stethoscopeData$.next(null);
                break;
            case DeviceType.THERMOMETER:
                this.thermometerData$.next(null);
                break;
            case DeviceType.BLOOD_PRESSURE:
                this.bloodPressureData$.next(null);
                break;
            case DeviceType.ECG:
                this.ecgData$.next(null);
                break;
        }

        // Clear errors for this device
        const currentErrors = this.deviceErrors$.value;
        if (currentErrors.has(deviceType)) {
            const updatedErrors = new Map(currentErrors);
            updatedErrors.delete(deviceType);
            this.deviceErrors$.next(updatedErrors);
        }
    }

    /**
     * Clear all device data
     */
    clearAllDeviceData(): void {
        this.pulseOximeterData$.next(null);
        this.stethoscopeData$.next(null);
        this.thermometerData$.next(null);
        this.bloodPressureData$.next(null);
        this.ecgData$.next(null);
        this.allDeviceData$.next(null);
        this.deviceErrors$.next(new Map());
    }

    /**
     * Get latest data for specific device (synchronous)
     */
    getLatestDeviceData<T>(deviceType: DeviceType): T | null {
        switch (deviceType) {
            case DeviceType.PULSE_OXIMETER:
                return this.pulseOximeterData$.value as T | null;
            case DeviceType.STETHOSCOPE:
                return this.stethoscopeData$.value as T | null;
            case DeviceType.THERMOMETER:
                return this.thermometerData$.value as T | null;
            case DeviceType.BLOOD_PRESSURE:
                return this.bloodPressureData$.value as T | null;
            case DeviceType.ECG:
                return this.ecgData$.value as T | null;
            default:
                return null;
        }
    }

    /**
     * Cleanup on service destroy
     */
    ngOnDestroy(): void {
        console.log('🧹 Cleaning up Device Data Manager Service');
        this.destroy$.next();
        this.destroy$.complete();
    }
}
