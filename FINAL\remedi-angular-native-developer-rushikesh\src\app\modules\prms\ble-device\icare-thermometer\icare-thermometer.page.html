<div style="border: 1px solid #007bff; padding: 16px; border-radius: 8px; background-color: #f8f9fa; position: relative;">
  <!-- Close button (optional UI) -->
  <button style="position: absolute; top: 8px; right: 8px; background: transparent; border: none; font-size: 20px; cursor: pointer;">
    &times;
  </button>

  <h3 style="color: #007bff;">🌡️ iCare Thermometer</h3>

  <button (click)="launchThermometer()" style="background-color: #007bff; color: white; padding: 12px 16px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">
    Measure Temperature
  </button>

  <div *ngIf="isReading" style="margin-top: 16px;">
    <p style="color: gray;">Waiting for thermometer data...</p>
  </div>

  <ng-container *ngIf="resultdata">
    <hr />

    <!-- Error message display -->
    <p *ngIf="resultdata.error" style="color: red;"><strong>Error:</strong> {{ resultdata.error }}</p>

    <!-- Temperature display -->
    <p><strong>Temperature:</strong> {{ resultdata.temperature }} °{{ resultdata.unit === 'fahrenheit' ? 'F' : 'C' }}</p>

    <!-- Timestamp -->
    <p><strong>Timestamp:</strong> {{ resultdata.timestamp | date: 'medium' }}</p>

    <!-- Battery Level (if available) -->
    <p *ngIf="resultdata.batteryLevel != null"><strong>Battery Level:</strong> {{ resultdata.batteryLevel }}%</p>

    <!-- Signal Quality (if available) -->
    <p *ngIf="resultdata.signalQuality != null"><strong>Signal Quality:</strong> {{ resultdata.signalQuality }}</p>

    <!-- Data Source -->
    <p><strong>Source:</strong> {{ resultdata.source }}</p>
  </ng-container>
</div>
