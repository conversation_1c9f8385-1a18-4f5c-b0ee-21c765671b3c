import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { ApiService } from 'src/app/core/services/api.service';
import { environment } from 'src/environments/environment';
import { ConstantService } from 'src/app/core/services/constant.service';
import Swal from 'sweetalert2';
import { Subscription } from 'rxjs';
import { OtpService } from 'src/app/core/services/otp.service';

interface OtpResponse {
  request_id?: string;
  type?: 'success' | 'failed';
  msg?: string;
  message?: string;
  status?: string;
}

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.page.html',
  styleUrls: ['./forgot-password.page.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule]
})
export class ForgotPasswordPage implements OnInit {
  valSubscription!: Subscription;
  validateNumberForm!: FormGroup;
  verifyOTPForm!: FormGroup;
  passwordForm!: FormGroup;

  submitted = false;
  showOtpSection = false;
  showPasswordSection = false;
  sendingOtp = false;

  domainName = environment.apiDomain;
  validPattern = '^$|^[A-Za-z0-9]+';

  constructor(
    private fb: FormBuilder,
    private apiSvc: ApiService,
    private constantSvc: ConstantService,
    private otpService: OtpService,
  ) {}

  ngOnInit() {
    this.createValidateNoForm();
    this.createVerifyOtpForm();
    this.createPasswordForm();
  }

  get fc() {
    return this.validateNumberForm.controls;
  }

  get fcOtp() {
    return this.verifyOTPForm.controls;
  }

  get fcPass() {
    return this.passwordForm.controls;
  }

  createValidateNoForm() {
    this.validateNumberForm = this.fb.group({
      mobileNum: ['', [Validators.required, Validators.pattern(this.validPattern)]],
    });
  }

  createVerifyOtpForm() {
    this.verifyOTPForm = this.fb.group({
      otp: ['', [Validators.required]]
    });
  }

  createPasswordForm() {
    this.passwordForm = this.fb.group({
      newPassword: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', Validators.required]
    });
  }

  sendOtp() {
    this.submitted = true;
    if (this.validateNumberForm.invalid || this.sendingOtp) return;

    this.sendingOtp = true;

    const username = this.validateNumberForm.value.mobileNum;
    this.otpService.sendOtp(username).subscribe({
      next: (res) => {
        this.sendingOtp = false;
        if (res.type === 'success') {
          this.showOtpSection = true;
          this.confirmDialog(res.msg || 'OTP sent successfully.');
        } else {
          this.errorDialog(res.msg || res.message || 'Failed to send OTP');
        }
      },
      error: () => {
        this.sendingOtp = false;
        this.errorDialog('API error while sending OTP.');
      }
    });
  }

  verifyOtp() {
    if (this.verifyOTPForm.invalid) return;

    const otp = this.verifyOTPForm.value.otp;
    const username = this.validateNumberForm.value.mobileNum;

    this.otpService.verifyOtp(username, otp).subscribe({
      next: (res) => {
        if (res.type === 'success') {
          this.showPasswordSection = true;
          this.showOtpSection = false;
          this.confirmDialog(res.message || 'OTP verified successfully.');
        } else {
          this.errorDialog(res.message || 'Invalid OTP');
        }
      },
      error: () => {
        this.errorDialog('API error while verifying OTP.');
      }
    });
  }

  changePassword() {
    if (this.passwordForm.invalid) return;

    const password = this.passwordForm.value.newPassword;
    const confirm = this.passwordForm.value.confirmPassword;

    if (password !== confirm) {
      this.errorDialog('Passwords do not match.');
      return;
    }

    const username = this.validateNumberForm.value.mobileNum;
    const query = `?username=${username}&domain=${this.domainName}&action=changePasswordForPatient&newpassword=${password}&usertype=Doctor&token=null`;

    this.apiSvc.post<OtpResponse>(this.constantSvc.APIConfig.GETCOMMONSERVICES + query, null, true).subscribe({
      next: (res) => {
        if (res.status === 'success') {
          this.confirmDialog(res.msg || 'Password changed successfully.');
        } else {
          this.errorDialog(res.msg || res.message || 'Password change failed.');
        }
      },
      error: () => {
        this.errorDialog('API error while changing password.');
      }
    });
  }

  confirmDialog(message: string) {
    Swal.fire({
      text: message,
      title: '',
      confirmButtonText: 'OK',
      icon: undefined,
      width: '300px',
      padding: '0.5em',
      heightAuto: false,
      showConfirmButton: true,
      showCloseButton: false,
      backdrop: true,
      customClass: {
        popup: 'tiny-popup',
        confirmButton: 'tiny-button',
        actions: 'align-end'
      }
    });
  }

  errorDialog(message: string) {
    Swal.fire({
      text: message,
      title: '',
      confirmButtonText: 'OK',
      icon: undefined,
      width: '300px',
      padding: '0.5em',
      heightAuto: false,
      showConfirmButton: true,
      showCloseButton: false,
      backdrop: true,
      customClass: {
        popup: 'tiny-popup',
        confirmButton: 'tiny-button',
        actions: 'align-end'
      }
    });
  }
}
