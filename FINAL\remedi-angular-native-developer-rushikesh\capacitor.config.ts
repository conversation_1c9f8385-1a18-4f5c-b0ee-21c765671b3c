import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'io.ionic.starter',
  appName: 'remedi-app',
  webDir: 'www',
  server: {
    // ✅ Allow external URLs for API calls
    allowNavigation: ['https://s3test2.remedi.co.in', 'https://*.remedi.co.in'],
  },
  plugins: {
    // ✅ Configure HTTP plugin for Android
    CapacitorHttp: {
      enabled: true,
    },
  },
  android: {
    // ✅ Allow clear text traffic for development
    allowMixedContent: true,
    captureInput: true,
  },
};

export default config;
