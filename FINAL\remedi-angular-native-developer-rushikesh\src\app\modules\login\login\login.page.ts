import { Component, OnInit } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormGroup,
  Validators,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { AuthService, LoginPayload } from 'src/app/core/services/auth.service';
import { ConstantService } from 'src/app/core/services/constant.service';
import { environment } from 'src/environments/environment';
import * as sha512 from 'js-sha512';
import { v4 as uuidv4 } from 'uuid';
import { ApiService } from 'src/app/core/services/api.service';
import { IcareDevicePage } from '../../prms/ble-device/icare-device/icare-device.page';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { IcareThermometerPage } from '../../prms/ble-device/icare-thermometer/icare-thermometer.page';

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
  standalone: true,
  imports: [
    IonicModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    MatDialogModule,
    TranslateModule,
    CommonModule,
  ],
})
export class LoginPage implements OnInit {
  domainName = environment.apiDomain;
  imageLogo = 'assets/images/default-logo.png';
  loginForm!: FormGroup;
  submitted = false;
  showLoginError = false;
  showLoginMsg = '';
  loading = false;

  constructor(
    private router: Router,
    private authService: AuthService,
    private fb: FormBuilder,
    private constantSvc: ConstantService,
    private apiSvc: ApiService,
    private dialog: MatDialog,
    private translate: TranslateService
  ) {}

  ngOnInit(): void {
    this.translate.setDefaultLang('en');
    this.translate.use('en');
    this.domainName = environment.apiDomain;
    this.getLogo();
    this.getDomainIdByDomainName();

    this.loginForm = this.fb.group({
      username: ['', Validators.required],
      password: ['', Validators.required],
    });
  }

  ionViewWillEnter() {
    this.resetLoginState();
  }

  resetLoginState() {
    this.loginForm.reset();
    this.submitted = false;
    this.showLoginError = false;
    this.loading = false;
  }

  goToForgot(): void {
    this.router.navigate(['/forgot-password']);
  }

  getLogo() {
    const token = localStorage.getItem('token') ?? 'null';
    const query = `?action=domainLogo&username=&domainName=${this.domainName}&token=${token}`;
    this.apiSvc.post(this.constantSvc.APIConfig.GETCOMMONSERVICES + query, null, true).subscribe({
      next: (res: any) => {
        if (res.status === 'success') {
          this.imageLogo = res.domainLogo;
        } else {
          console.warn('Failed to load logo:', res);
        }
      },
      error: (err) => {
        console.error('API error loading logo:', err);
      }
    });
  }

  getDomainIdByDomainName(): void {
    const queryParams = `?action=DomainIdByDomainName&domainName=${this.domainName}`;
    const apiUrl = this.constantSvc.APIConfig.GETCOMMONSERVICES + queryParams;

    this.apiSvc.post<any>(apiUrl, null, true).subscribe({
      next: (res) => {
        if (res.status === 'success') {
          localStorage.setItem('domainId', res.domainid.toString());
          localStorage.setItem('language', res.language);
          localStorage.setItem('offset', res.offset);
        } else {
          console.warn('Domain lookup failed');
        }
      },
      error: (err) => {
        console.error('Error getting domain ID:', err);
      }
    });
  }

  private SHA512(data: string): string {
    return sha512.sha512(data);
  }

  private generateUUID(): string {
    return uuidv4();
  }

  private getRMD(): string {
    const parts = 'nbbeubrosbybbnbapbbbtbibbcbkbbebyb'.split('b');
    return parts.join('');
  }

  login(): void {
    this.submitted = true;
    this.showLoginError = false;

    if (this.loginForm.invalid || this.loading) return;

    this.loading = true;

    const password = this.loginForm.value.password;
    const hashedPassword = sha512.sha512(password + this.getRMD());
    const clientId = uuidv4();

    const payload: LoginPayload = {
      action: 'loginservices',
      username: this.loginForm.value.username,
      password: hashedPassword,
      offset: environment.getTimeOffSet?.() ?? '5:30',
      domainName: this.domainName,
      requestFromAngular: 'requestFromAngular',
      clientId,
    };

    this.authService.login(payload).subscribe({
      next: (res) => {
        if (res.status === 'success' && res.token) {
          localStorage.setItem('client_Id', clientId);
          localStorage.setItem('token', res.token);

          if (res.profiledetail) {
            localStorage.setItem('USER', JSON.stringify(res.profiledetail));
            if (res.profiledetail.language) {
              localStorage.setItem('language', res.profiledetail.language);
              this.translate.use(res.profiledetail.language);
            }
          }

          if (res.commondetail) {
            localStorage.setItem('COMMON_DETAIL', JSON.stringify(res.commondetail));
          }

          this.router.navigate(['/parameter']);
        } else {
          this.showLoginError = true;
          this.showLoginMsg = res.msg || 'Invalid credentials';
        }

        this.loading = false;
      },
      error: (err) => {
        console.error('Login error:', err);
        this.showLoginError = true;
        this.showLoginMsg = 'Login API error';
        this.loading = false;
      },
    });
  }




}
