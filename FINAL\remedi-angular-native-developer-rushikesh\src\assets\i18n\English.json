{"Welcome": "Welcome", "Railway_Employee": "Railway Employee", "Prescription": "Prescription", "Advise": "Advise", "User_Details_Updated": "User details has been updated.", "Report_Start_Date_Configuration": "Report Start Date Configuration", "Report_Start_Date_Updated_Success": "Report Start Date Updated Successfully", "Issue_In_Report_Start_Date_Update": "Issue In Report Start Date Update.", "ob_gyn": "I, ~doctor_name~, declare that while conducting the <br> evaluation of the ultrasound image scanning,<br> I have neither detected nor disclosed the sex <br> of the foetus to anybody in any manner.", "please_select": "Please select", "COMPLETE": "Complete", "INCOMPLETE": "Incomplete", "Change_language_of_all_users_in_this_domain": "Change language of all users in this domain.", "FOLLOWUP": "FOLLOWUP", "NEW": "NEW", "Enter_Height_upto_two_decimal_pionts": "Enter correct height.", "Enter_Weight_upto_three_decimal_pionts": "Enter correct weight.", "Select_Subchapter": "Select Sub Chapter", "Blood_Presure": "Blood Pressure", "Ultrasound": "Ultrasound", "Reconnecting": "Reconnecting.......", "Server_session_has_been_closed_Please_login_again": "Server session has been closed. Please login again.", "Make_sure_digital_pen_service_is_installed_or_running": "Make sure digital pen service is installed or running.", "something_went_wrong": "something went wrong", "Mail_Copy_has_been_successfully": "Mail Copy has been sent successfully.", "Sending_Mail_Copy_Failed": "Sending Mail Copy Failed.", "something_went_wrong_when_sending_mail_report": "something went wrong when sending mail report", "Please_upload_files_having_extensions": "Please upload files having extensions", "Taking_Too_Long_To_Print_Retry_Later": "Taking Too Long To Print.. Retry Later", "Print_Success": "Print Success", "No_Response_From_Thermal_Printer_Make_Sure_Thermal_Printer_Service_is_Running": "No Response From Thermal Printer!Make Sure Thermal Printer Service is Running.", "Printer_Device_Not_Connected": "Printer Device Not Connected.", "Double_Quote_Response_From_Printer": "Double Quote Response From Printer.", "Blank_Response_From_Printer": "Blank Response From Printer.", "No_Response_From_Printer_Make_Sure_Service_is_Running": "No Response From Printer!Make Sure Service is Running.", "Invalid_Complaint": "<PERSON><PERSON><PERSON>", "Please_answer_all_questions": "Please answer all questions!", "Data_saved_successfully": "Data saved successfully!", "Oops_something_went_wrong": "Oops, something went wrong!", "Please_provide_medicine_name": "Please provide medicine name!", "This_is_not_implemented_yet": "This is not implemented yet", "Recording_Fetal_Data": "Recording Fetal Data", "sensor_connected_successfully": "sensor connected successfully!", "error_while_taking_ECG_reading": "error while taking ECG reading", "Problem_while_start_stop_scan": "Problem while start/stop scan.Please reconnect dongle and refresh page", "start_scan_status": "start scan status", "stop_scan_status": "stop scan status", "Your_Image": "Your Image", "Mobile_number": "Mobile number", "Consumables": "Consumables", "Data_Dump": "Data Dump", "All_Doctors_in_this_domain": "All Doctor's in this domain", "Please_Switch_On_SpiroSensor_Device": "Please Switch On SpiroSensor Device", "Please_Switch_On_StethoSensor_Device": "Please Switch On StethoSensor Device", "please_select_from_date_first": "please select from date first", "Record_saved_successfully": "Record saved successfully", "Strip": "Strip", "Test_Result": "Test Result", "Reference_Strip": "Reference Strip", "Urine_Test": "<PERSON><PERSON>", "Reading": "Reading", "Blood_Pressure": "Blood Pressure", "Rapid_Reader": "Rapid Reader", "Test_Image_Not_Found": "Test Image Not Found", "Computing_result": "Computing result", "hrs": "hrs", "AND_Test_Code": "AND Test Code", "already_exists": "already exists", "Zoom_In": "Zoom In", "Zoom_Out": "Zoom Out", "Rotate": "Rotate", "Prev": "Prev", "Next": "Next", "Waiting_for_doctor_to_connect": "Waiting for doctor to connect.", "Connected_with_Doctor": "Connected with <PERSON>.", "Something_went_wrong_please_relogin": "Something went wrong please relogin.", "Waiting_for_Patient_to_connect": "Waiting for <PERSON><PERSON> to connect.", "Connected_with_Patient": "Connected with <PERSON><PERSON>.", "Some_error_to_handle": "Some error to handle", "Optical_Reader_device_Not_Detected": "Optical Reader device Not Detected", "Result_cannot_be_determined": "Result cannot be determined", "Slide_not_Present_in_tray": "Slide not Present in tray", "Directory_not_created": "Directory not created", "Could_Not_Open_ROI_Image": "Could Not Open ROI Image", "Invalid_Test": "Invalid Test", "Slide_Not_inserted_Properly": "Slide Not inserted Properly", "Test_Type_Not_Implemented": "Test Type Not Implemented", "Could_Not_Open_Source_Image": "Could Not Open Source Image", "Something_Problem_With_Camera": "Something Problem With Camera", "Could_not_open_Camera": "Could not open Camera", "Device_connected": "Device connected", "Capture": "Capture", "Error_with_UrineTest_Try_Again": "Error with <PERSON><PERSON>. Try Again.", "ERROR_Failed_to_detect_test_strip": "ERROR", "ERROR_Time_Over_Please_insert_tray_strip_in_lessthan_30seconds": "ERROR", "UrineTest_Cancelled_Check_connection_camera": "Urine Test is Cancelled. Please Check connection of camera.", "Please_TURN_on_device": "Please TURN ON the device", "Please_follow_the_instructions_on_device": "Please follow the instructions on the device", "Connect_Device_to_another_port": "Connect Device to another port.", "Device_is_incompitable": "Device is incompitable", "Record_not_found": "Record not found.", "ERROR_Failed_to_detect_reference_strip_Please_dont_remove_Tray_or_Strip": "ERROR", "Caution_For_better_accuracy_retake_reding": "Caution", "Stethoscope_device_connected_successfully": "Stethoscope device connected successfully.", "Make_sure_Stethoscope_Device_is_paired_and_switchedON": "Make sure Stethoscope Device is paired and switched ON.", "Make_sure_Stethoscope_device_is_paired": "Make sure Stethoscope device is paired.", "Please_wait_Connecting_Stethoscope_device": "Please wait. Connecting Stethoscope device....", "Enter_Valid_LDL_reading": "Enter Valid LDL reading", "Failed_to_Display_Spirometry_Past_Data": "Failed to Display Spirometry Past Data", "Past_Records_Fetching_Failed": "Past Records Fetching Failed", "Fail_to_save_writing_pad_content": "Fail to save writing pad content.", "Writingpad_content_successfully_saved": "Writing pad content successfully saved.", "Offline_Data_Available": "Offline Data Available", "Makesure_ewritemate_device_is_connected": "Make sure e-writemate device is connected to system through usb cable.", "WritingPad_Failed_to_load_close_and_open_it_again": "Writing Pad Failed to load. Kindly,close and open it again.", "image_saved": "image saved", "Problem_in_Inserting_Consultation_Fee": "Problem in Inserting Consultation Fee", "Problem_in_saving_Consultation_Fee": "Problem in saving Consultation Fee", "Please_provide_only_integer_number": "Please provide only integer number", "Please_wait_uploading_image": "Please wait, uploading image....", "Error_while_uploading_image_Please_try_again": "Error while uploading image,Please try again!", "Image_uploaded_successfully": "Image uploaded successfully !", "Invalid_contact_person_lastname": "Invalid contact person last name.", "Invalid_contact_person_firstname": "Invalid contact person first name.", "Invalid_ProjectId": "Invalid ProjectId", "Invalid_email": "<PERSON><PERSON><PERSON>.", "DoubleQuote_Response_From_Printer": "Double Quote Response From Printer.", "NoResponse_From_ThermalPrinter": "No Response From Thermal Printer!Make Sure Thermal Printer Service is Running.", "An_unknown_error_occurred_while_trying_to_publish_your_video_Please_try_again": "An unknown error occurred while trying to publish your video. Please try again later.", "is_connected_and_notbeing_used_by_another_application_and_try_again": "is connected and not being used by another application and try again.", "Please_check_that_your_webcam": "Please check that your webcam", "Failed_to_get_access_to_your_camera_or_microphone": "Failed to get access to your camera or microphone.", "Publishing_your_video_failed_This_due_to_restrictive_firewall": "Publishing your video failed. This could be due to a restrictive firewall.", "Publishing_your_video_failed": "Publishing your video failed. You are not connected to the internet.", "Your_publisher_lost_its_connection_try_publishing_again": "Your publisher lost its connection. Please check your internet connection and try publishing again.", "Media_access_denied": "Media access denied!", "Please_allow_access_to_Camera_Microphone": "Please allow access to the Camera and Microphone and try publishing again.", "ECG_Unreachable": "ECG device unreachable.Pleace keep device nereby and start reading again.", "Provisional": "Provisional", "Your_Generate_Password_is": "Your Generate Password is", "Please_wait": "Please wait", "You_cannot_start_pending_or_review_consultation": "You can not start pending or in review consultation.", "cannot_start_reviewed_consultation": "cannot start reviewed consultation", "If_Evolute_Service_Utility_is_Running": "If Evolute Service/Utility is Running", "Select_Complaint": "Select Complaint", "Select_Pharmacy_Name_From_List": "Select Pharmacy Name From List", "Wait_while_Printing": "Wait While Printing.....", "Please_Wait_while_Printing": "Please Wait while Printing....", "Mobile": "Mobile", "PID": "PID", "record_already_exist": "record(s) already exist", "Please_Provide_Projectid": "Please Provide Project Id", "Record_already_exists": "Record already exists", "problem_while_inserting": "problem while inserting", "problem_occured_while_updating": "problem occured while updating", "problem_occured_while_inserting_Brand": "problem occured while inserting Brand", "problem_occured_while_inserting_Drug_Class": "problem occured while inserting Drug Class", "problem_occured_while_inserting_Drug_Form": "problem occured while inserting Drug Form", "Approve_Number_cannot_be_greater_than_Requested": "Approve Number cann't be greater than Requested", "problem_occured_while_inserting_category": "problem occured while inserting category", "already_exist_Please_Try_another_name": "already exist Please Try another name", "problem_occured_while_inserting_lab": "problem occured while inserting lab", "This_Pharmacy_Detail_already_exists": "This Pharmacy Detail already exists", "Record_is_not_available_to_update": "Record is not available to update", "Doctor_Record_Not_Found": "Doctor Record Not Found", "Doctor_registration_limit_has_been_exceeded": "Doctor registration limit has been exceeded", "problem_while_inserting_in_doctorprofile": "problem while inserting in doctorprofile", "problem_while_inserting_in_loginDetails": "problem while inserting in loginDetails", "problem_occured_while_updating_doctor_profile": "problem occured while updating doctor profile", "You_can_not_enable_Doctor_Given_limit_has_been_exceeded": "You can not enable Doctor Given limit has been exceeded", "Nurse_registration_limit_has_been_exceeded": "Nurse registration limit has been exceeded", "problem_while_inserting_nurse_profile": "problem while inserting nurse profile", "error_while_storing_nurse_data": "error while storing nurse data", "You_can_not_enable_Nurse_Given_limit_has_been_exceeded": "You can not enable Nurse Given limit has been exceeded", "problem_while_getting_data": "problem while getting data", "disconnected": "disconnected", "completed": "completed", "failure": "failure", "suspended": "suspended", "resumed": "resumed", "failure_to_update": "failure to update", "Cancel": "Cancel", "suspendedbydoctor": "suspendedbydoctor", "failure_to_suspend": "failure to suspend", "successfully_sent_this_case_for_review": "successfully sent this case for review!", "failure_to_send_review": "failure to send review", "Please_provide_consultationid_and_reviewby": "Please provide consultationid and reviewby", "Provide_all_the_details": "Provide all the details", "Error_while_inserting_data": "Error while inserting data", "Provide_valid_Id": "Provide valid Id", "Some_slots_are_already_available_in_the_selected_time_range_so_Please_modify_your_selection": "Some slots are already available in the selected time range so Please modify your selection", "slots_already_available": "slots already available", "error_while_adding_slots": "error while adding slots", "slots_not_found": "slots not found", "success": "success", "Wrong_Request": "Wrong Request", "Comment_saved_successfully": "Comment has been saved successfully.", "Comment_saving_failed": "Comment saving failed", "failed": "failed", "This_medicine_is_already_exist": "This medicine is already exist", "problem_occured_while_inserting_bmq_q1": "problem occured while inserting bmq q1", "exception_occured_while_inserting_bmq_q1": "exception occured while inserting bmq q1", "problem_occured_while_updating_bmq_q1": "problem occured while updating bmq q1", "problem_occured_while_deleteing_bmq_q1": "problem occured while deleteing bmq q1", "problem_occured_while_inserting_bmq_q3": "problem occured while inserting bmq q3", "exception_occured_while_inserting_bmq_q3": "exception occured while inserting bmq q3", "problem_occured_while_updating_bmq_q3": "problem occured while updating bmq q3", "problem_occured_while_deleteing_bmq_q3": "problem occured while deleteing bmq q3", "problem_occured_while_inserting_bmq_q2": "problem occured while inserting bmq q2", "exception_occured_while_inserting_bmq_q2": "exception occured while inserting bmq q2", "problem_occured_while_updating_bmq_q2": "problem occured while updating bmq q2", "problem_occured_while_deleteing_bmq_q2": "problem occured while deleteing bmq q2", "problem_occured_while_inserting_complaints": "problem occured while inserting complaints", "record(s)_already_exist": "record(s) already exist", "problem_occured_while_updating_complaint": "problem occured while updating complaint", "problem_occured_while_deleteing_complaint": "problem occured while deleteing complaint", "diagnosis_already_prescribed": "diagnosis already prescribed", "ICD_code_already_present": "ICD code already present", "problem_occured_while_inserting_diagnosis": "problem occured while inserting diagnosis", "problem_occured_while_updating_diagnosis": "problem occured while updating diagnosis", "problem_occured_while_deleteing_diagnosis": "problem occured while deleteing diagnosis", "problem_occured_while_inserting_recommendations": "problem occured while inserting recommendations", "problem_occured_while_updating_recommendations": "problem occured while updating recommendations", "problem_occured_while_deleteing_recommendations": "problem occured while deleteing recommendations", "problem_occured_while_inserting_invetigation": "problem occured while inserting invetigation", "test(s)_already_prescribed": "test(s) already prescribed", "problem_occured_while_updating_investigation": "problem occured while updating investigation", "problem_occured_while_deleting_investigation": "problem occured while deleting investigation", "Not_Sufficient_Stock_contact_admin": "Not Sufficient Stock contact admin", "problem_while_inserting_ecg_data_into_master_table": "problem while inserting ecg data into master table", "problem_while_inserting_ecg_filename_into_database": "problem while inserting ecg filename into database", "problem_while_inserting_stetho_data_into_master_table": "problem while inserting stetho data into master table", "problem_while_inserting_stetho_filename_into_database": "problem while inserting stetho filename into database", "problem_while_inserting_spo2_filename_into_database": "problem while inserting spo2 filename into database", "problem_while_inserting_spiro_data_into_master_table": "problem while inserting spiro data into master table", "problem_while_inserting_fetal_data_into_master_table": "problem while inserting fetal data into master table", "problem_while_inserting_fetal_filename_into_database": "problem while inserting fetal filename into database", "problem_while_RETRIVING_MAX_FROM_FETAL_filename_into_database": "problem while RETRIVING MAX FROM FETAL filename into database", "problem_while_RETRIVING_MAX_FROM_stetho_filename_into_database": "problem while RETRIVING MAX FROM stetho filename into database", "problem_while_RETRIVING_MAX_FROM_ECG_BLE_filename_into_database": "problem while RETRIVING MAX FROM ECG BLE filename into database", "problem_while_RETRIVING_MAX_FROM_ECG_filename_into_database": "problem while RETRIVING MAX FROM ECG filename into database", "problem_while_RETRIVING_MAX_FROM_spo2_filename_into_database": "problem while RETRIVING MAX FROM spo2 filename into database", "problem_while_inserting_patient_monitor_data_into_master_table": "problem while inserting patient monitor data into master table", "problem_while_inserting_patient_Monit_FileName_into_database": "problem while inserting patient <PERSON>it <PERSON>N<PERSON> into database", "saved": "saved", "problem_occured_while_inserting_referral": "problem occured while inserting referral", "problem_occured_while_updating_referrals": "problem occured while updating referrals", "problem_occured_while_deleting_referrals": "problem occured while deleting referrals", "Tests": "Tests", "Finger_does_not_matched": "<PERSON><PERSON> does not matched!", "An_error_occurred_while_loading_device_libraries": "An error occurred while loading device libraries", "Place_your_finger_properly": "Place your finger properly", "No_error": "No error", "JSGFPLib_object_creation_failed": "JSGFPLib object creation failed", "Function_call_failed": "Function call failed", "Invalid_parameter_used": "Invalid parameter used", "Not_used_function": "Not used function", "DLL_loading_failed": "DLL loading failed", "Device_driver_loading_failed": "Device driver loading failed", "Algorithm_DLL_loading_failed": "Algorithm DLL loading failed", "Cannot_find_driver_sys_file": "Cannot find driver sys file", "Chip_initialization_failed": "Chip initialization failed", "Image_data_lost": "Image data lost", "Image_capture_timeout": "Image capture timeout", "Device_not_found": "Devi<PERSON> not found", "Driver_file_load_failed": "Driver file load failed", "Wrong_image": "Wrong image", "Lack_of_USB_bandwidth": "Lack of USB bandwidth", "Device_is_already_opened": "Device is already opened", "Serial_number_does_not_exist": "Serial number does not exist", "Unsupported_device_Extract_&_Matching_Error_Codes": "Unsupported device Extract & Matching Error Codes", "Inadequate_number_of_minutiae": "Inadequate number of minutiae", "Wrong_template_type": "Wrong template type", "Error_in_decoding_template_1": "Error in decoding template 1", "Error_in_decoding_template_2": "Error in decoding template 2", "Extraction_failed": "Extraction failed", "Matching_failed": "Matching failed", "Undefined_error_condition": "Undefined error condition!", "Place_finger_on_device": "Place finger on device!", "Finger_print_captured_successfully": "Finger print captured successfully!", "Error_while_parsing_finger_print_data": "Error while parsing finger print data!", "Error_while_register_finger_print": "Error while register finger print!", "Please_place_registered_finger_on_device": "Please place registered finger on device!", "Error_while_verifying_finger_print": "Error while verifying finger print!", "Finger_print_not_registered": "Finger print not registered!", "View_Report": "View Report", "View_Foot_Report": "View Foot Report", "Start_Debugging": "Start Debugging", "Stop_Debugging": "Stop Debugging", "Loading_Camera": "Loading Camera", "Camera": "Camera", "Retry": "Retry", "Take_snapshot": "Take snapshot", "Retake": "Retake", "Please_connect_camera": "Please connect camera", "Please_allow_access_to_camera": "Please allow access to camera", "Make_sure_camera_is_connected_and_accessible": "Make sure camera is connected and accessible", "Patient_Delivery_Address": "Patient Delivery Address", "Phone_Number": "Phone Number", "Please_Enter_The_Phone_Number": "Please Enter The Phone Number", "Please_Select_The_Pharmacy_Name": "Please Select The Pharmacy Name", "Print_in_Thermal_Printer": "Print in Thermal Printer", "Print": "Print", "Test_Name": "Test Name", "Units": "Units", "Results": "Results", "Reference_Value_Range": "Reference Value Range", "Invoice": "Invoice", "Price": "Price", "Description": "Description", "Parameter": "Parameter", "Sr": {"No": {"": "Sr.No."}}, "This_is_an_electronically_generated_invoice": "This is an electronically generated invoice", "Additional_Notes": "Additional Notes", "Test_fees": "Test fees", "Consultation_Fee": "Consultation Fee", "Total": "Total", "years": "years", "months": "months", "days": "days", "Capture_Finger_Print": "Capture Finger Print", "Consultation_Fee_Saved_Successfully": "Consultation Fee Saved Successfully", "OR": "OR", "Please_select_nurse": "Please select nurse", "From_Hour": "From Hour", "From_Minute": "From Minute", "Till_Hour": "<PERSON>", "Till_Minute": "<PERSON>", "please_select_chapter": "please select chapter", "Please_select_diagnosis_category": "Please select diagnosis category", "Please_select_subchapter": "Please select subchapter", "Select_Referral": "Select Referral", "Select_Category": "Select Category", "Select_Recommendations": "Select Recommendations", "Select_Lab": "Select Lab", "Select_Test": "Select Test", "Select_Instruction": "Select Instruction", "Select_Medicine": "Select Medicine", "Select_Diagnosis": "Select Diagnosis", "Select_Chapter": "Select Chapter", "Recent_Diagnosis": "Recent Diagnosis", "Click_On_Diagnosis_Name_To_Add": "Click on Diagnosis  name no add", "Recent_Complaints": "Recent Complaints", "click_on_complaint_name_to_add": "click on complaint name to add", "Since": "Since", "Complaint_Testing": "Complaint Testing", "Recently_Prescribed": "Recently Prescribed", "click_on_medicine_name_to_add": "click on medicine name to add", "click_on_investigations_name_to_add": "click on investigations name to add", "Recent_investigations": "Recent investigations", "click_on_instructions_name_to_add": "click on instructions name to add", "Recent_instructions": "Recent instructions", "click_on_referral_name_to_add": "click on referral name to add", "Recently_Referred": "Recently Referred", "Hours": "Hours", "Days": "Days", "Weeks": "Weeks", "Months": "Months", "Years": "Years", "Chapter": "Chapter", "Subchapter": "Subchapter", "Chapter_wise": "Chapter wise", "Code": "Code", "Thermal_Printer": "Thermal Printer", "Mail_Report": "Mail Report", "Referral_Note": "Referral Note", "Enter_Note": "Enter Note", "Please_enter_referral_note": "Please enter referral note", "Please_enter_the_referral_note_within_the_allowed_limit": "Please enter the referral note within the allowed limit", "Enter_Referral_Note": "Enter Referral Note", "Upload_Domain_Logo": "Upload Domain Logo", "Parameter_Fee_Configuration": "Parameter Fee Configuration", "Parameter_Configuration": "Parameter Configuration", "Follow_Up": "Follow Up", "Medicine_Stock": "Medicine Stock", "Test_Fee_Configuration": "Test Fee Configuration", "Upload_Complaints": "Upload Complaints", "Download_Complaints_Template": "download complaints template", "Upload": "Upload", "Close": "Close", "Generate_Password": "Generate Password", "Reset_Password": "Reset Password", "Upload_Medicine": "Upload Medicine", "Download_Medicine_Template": "Download Medicine Template", "Upload_Recommendations": "Upload Recommendations", "Add_Logo": "Add Logo", "Add_Hospital_Name": "Add Hospital Name", "Add_Login_Logo_Name": "Add Login Screen Logo", "Follow-up_Discount": "Follow-up Discount", "Save": "Save", "Upload_Intervention": "Upload Intervention", "Download_Intervention_Template": "Download Intervention Template", "Download_Recommondations_Template": "Download Recommondations Template", "Select_Doctor": "Select Doctor", "Nurse": "Nurse", "Save_Discount": "Save Discount", "Upload_Medicine_Stock": "Upload Medicine Stock", "Upload_TestIntervention": "Upload TestIntervention", "Generate_TestPrices_Template": "Generate TestPrices Template", "Country": "Country", "Generate_Stock_Template": "Generate Stock Template", "chief_complaints": "Chief <PERSON><PERSON><PERSON><PERSON>", "please_enter_chief_complaint": "Please enter chief complaint", "data_saved_successfully": "Data saved successfully!", "problem_while_saving_chief_complaint": "Problem while saving chief complaint", "Initializing": "Initializing....", "PleaseWait": "PleaseWait.....", "Tray_with_cassette_is_inserted_properly": "Tray with cassette is inserted properly.", "Makesure_device_connected_and_switched_ON": "Make sure device is connected and switched ON.", "error_while_taking_BP": "error while taking BP.", "Measurement_Error": "Measurement Error", "Pulse_rate_Error": "Pulse rate Error", "Too_low_BP_(beyond_range)": "Too low BP (beyond range)", "Very_high_BP_(beyond_range)": "Very high BP (beyond range)", "Overpressure_Error_The_cuff_pressure_exceeded_280mmHg": "Overpressure Error.The cuff pressure exceeded 280mmHg", "Deflation_Error": "Deflation Error", "Inflation_Error": "Inflation Error", "Cuff_is_not_connected": "<PERSON><PERSON> is not connected", "Lead_not_connected": "Lead(s) not connected. <br> Please connect all leads properly and take reading again.", "StethoScope": "StethoScope", "Error_while_saving_data": "Error while saving data.", "Waiting_for_Sensor_to_Connect": "Waiting for <PERSON><PERSON> to Connect", "Otoscope": "Otoscope", "Please_SwitchOn_StethoSensor_Device": "Please Switch On StethoSensor Device.", "Please_take_proper_reading": "Please take proper reading", "Enter_Valid_Glucose_percentage": "Enter Valid Glucose percentage", "Gain_Setting": "<PERSON><PERSON>", "Patient_Monitor": "Patient Monitor", "Dispensed": "Dispensed", "Stock": "Stock", "Expiry": "Expiry", "Batch": "<PERSON><PERSON>", "Serial_No": "Sr.No", "Stock_Information": "Stock Information", "Dose": "<PERSON><PERSON>", "No_Of_Days": "No.Of.Days", "Stock_updated_successfully": "Stock updated successfully!", "Error_while_updating_stock": "Error while updating stock!", "Please_update_stock": "Please update stock!", "freetext_entry_OR_Not_in_inventory": "May be free text entry OR Not in inventory!", "only": "only.", "Interventions_uploaded_successfully": "Interventions has been uploaded successfully", "Complaints_uploaded_successfully": "Complaints has been uploaded successfully", "Recommendations_uploaded_successfully": "Recommendations has been uploaded successfully", "There_was_an_error": "There was an error", "Request_doesnot_contain_uploaddata": "Request does not contain upload data", "TestIntervention_uploaded_successfully": "TestIntervention has been uploaded successfully", "Medicines_uploaded_successfully": "Medicines has been uploaded successfully", "Uploaded_excelformat_not_correct_duplicaterow_entry": "Uploaded excel format is not correct or duplicate row entry", "Please_upload_file": "Please upload file", "Reduce_Stock": "Reduce Stock", "Add_Stock": "Add Stock", "No_data_available": "No data available!", "Fee": "Fee", "Parameter_Price": "Parameter Price", "Approve_Paid_Coupons": "Approve Paid Coupons", "Available_Free_Coupons": "Available Free Coupons", "Available_Paid_Coupons": "Available Paid Coupons", "Requested_Paid_Coupons": "Requested Paid Coupons", "Nurse_Username": "Nurse <PERSON><PERSON><PERSON>", "Select_District": "Select District", "Select_State": "Select State", "Select_Country": "Select Country", "Select_Form": "Select Form", "Select_Class": "Select Class", "Select_Brand": "Select Brand", "Your_followup_discount_added_successfully": "Your follow up discount added successfully.", "please_enter_followup_discount": "please enter follow up discount", "please_select_first_username": "please select first username", "Capture_finger_print": "Capture Finger Print", "Connect": "Connect", "record_not_found": "Record not found", "device_is_not_reachable": "Device is not reachable", "Problem_generate_new_password": "Problem to generate new password", "Select_DateFormat": "Select DateFormat", "Select_Speciality": "Select Speciality", "Display_Date_Format": "Display Date Format", "Download_Stock_Template": "Download Stock Template", "download_test_intervention_template": "download test intervention template", "Not_Generated": "Not Generated", "Generated_Successfully": "Generated Successfully", "Available": "Available", "Out_of_stock": "Out of stock", "ExpiryDate": "Expiry Date", "Batch_Number": "Batch Number", "Available_Stock": "Available Stock", "Medicine_Name": "Medicine Name", "Please_enter_followup_discount_in_percentage": "Please enter follow up discount in %", "Select_Nurse": "Select Nurse", "Upload_File_Size_lessthan_2Mb": "Upload file size should be less than 2Mb.", "Test": "Test", "Select_LabName": "Select LabName", "Disabled": "Disabled", "Enabled": "Enabled", "ACR_ManualEntry": "ACR Manual Entry", "Select_Result": "Select Result", "select_recommendations": "select recommendations", "UrineTest_Results_Saved_Successfully": "Urine Test Results Saved Successfully.", "Please_Initialize_again": "Please Initialize again.", "Please_connect_the_device": "Please connect the device and make sure tray with reference strip is inserted properly.", "Urine_Note": "Note", "Insert_tray_with_prepared_teststrip_in_device": "Insert the tray with prepared test strip in the device.", "Place_teststrip_on_tray_proper_position": "Place the test strip on the tray in proper position", "Remove_excess_urine": "Remove excess urine", "Click_startbutton_when_removing_teststrip_from_urinesample": "Click the start button exactly at the time when you remove the test strip from urine sample", "Dip_teststrip_in_Urinesample": "Dip test strip in Urine sample", "Computing_Result": "Computing Result", "HBA1C_ManualEntry": "HbA1c Manual Entry", "Please_SwitchOn_FetalSensor_Device": "Please Switch On FetalSensor Device.", "Spirometry": "Spirometry", "Please_SwitchOn_SpiroSensor_Device": "Please Switch On SpiroSensor Device.", "SpiroMeter_ManualEntry": "SpiroMeter Manual Entry", "LipidProfile": "Lipid Profile", "MultiCareProfile": "F200 analyzer Profile", "MultiCare_ManualEntry": "F200 analyzer Manual Entry", "Lipid_ManualEntry": "Lipid Manual Entry", "UrineTest_ManualEntry": "Urine Test Manual Entry", "RapidTest_ManualEntry": "Rapid Test Manual Entry", "Glucose_ManualEntry": "Glucose Manual Entry", "Please_SwitchOn_Spo2Sensor": "Please Switch On Spo2Sensor Device.", "BloodPressure_ManualEntry": "BloodPressure Manual Entry", "Please_SwitchOn_BPSensorDevice": "Please Switch On BPSensor Device.", "Please_SwitchOn_ECGSensorDevice": "Please Switch On ECGSensor Device.", "PulseOximeter_ManualEntry": "PulseOximeter Manual Entry", "Please_Switch_On_Temperature_Device": "Please Switch On Temperature Device.", "Temperature_Manual_Entry": "Temperature Manual Entry", "Failed_to_save_UrineTest_ManualEntrydata": "Failed to save Urine Test Manual Entry data.", "Successfully_saved_Urine_Test_Manual_Entry_data": "Successfully saved Urine Test Manual Entry data.", "Please_proper_GLU_value_format": "Please proper GLU value format", "Please_proper_KET_value_format": "Please proper KET value format", "Please_proper_BLO_value_format": "Please proper BLO value format", "Please_proper_PRO_value_format": "Please proper PRO value format", "Please_proper_LEU_value_format": "Please proper LEU value format", "Please_select_NIT_test_result": "Please select NIT test result from dropdown", "Failed_to_save": "Failed to save", "data": "data.", "Successfully_saved": "Successfully saved", "Please_select_testresult_from_dropdown": "Please select test result from dropdown", "Please_select_TestType_from_dropdown": "Please select TestType from dropdown", "Please_wait_fetching_result_from_device": "Please wait, We are fetching result from device!", "Failed_to_save_Fetal_data": "Failed to save Fetal data.", "Successfully_saved_Fetal_data": "Successfully saved Fetal data.", "Enter_Valid_HeartRate_Reading": "Enter Valid Heart Rate Reading", "Result_fetched_successfully": "Result fetched successfully!", "Error_while_saving_Hba1c_data": "Error while saving HbA1c data.", "Successfully_saved_Hba1c_data": "Successfully saved HbA1c data.", "Please_enter_hemoglobin_percentage": "Please enter hemoglobin %", "Successfully_saved_Lipid_data": "Successfully saved Lipid data.", "Enter_Valid_Triglycerides_Cholestrol_reading": "Enter Valid Triglycerides Cholestrol reading", "Enter_Valid_HDL_reading": "Enter Valid HDL reading", "Enter_Valid_Total_Cholestrol_reading": "Enter Valid Total Cholestrol reading", "Successfully_saved_Spirometer_data": "Successfully saved Spirometer data.", "Enter_Valid_TLC_rate": "Enter Valid TLC rate", "Enter_Valid_TV_rate": "Enter Valid TV rate", "Enter_Valid_PEF_rate": "Enter Valid PEF rate", "Enter_Valid_FEF75_rate": "Enter Valid FEF75 rate", "Enter_Valid_FEF50_rate": "Enter Valid FEF50 rate", "Enter_Valid_FEF25_rate": "Enter Valid FEF25 rate", "Enter_Valid_FEV_Percentage_rate": "Enter Valid FEV Percentage rate", "Enter_Valid_FEV1_reading": "Enter Valid FEV1 reading", "Enter_Valid_FVC_reading": "Enter Valid FVC reading", "Successfully_saved_bloodpressure_data": "Successfully saved blood pressure data.", "Enter_Valid_diastolic_reading": "Enter Valid diastolic reading", "Enter_Valid_systolic_reading": "Enter Valid systolic reading", "Successfully_saved_ACR_data": "Successfully saved ACR data.", "Triglycerides": "Triglycerides", "Successfully_saved_Temperature_data": "Successfully saved Temperature data.", "Enter_Valid_entryof_fahrenheit": "Enter Valid enty in fahrenheit", "Successfully_saved_Spo2_data": "Successfully saved Spo2 data.", "Values_cannot_be_empty": "Values can't be blank.", "Enter_Valid_pulse": "Enter Valid pulse", "Enter_Valid_oxygen_percentage": "Enter Valid oxygen percentage", "Successfully_saved_Glucose_data": "Successfully saved Glucose data.", "Enter_Valid_Glucose_Value": "Enter Valid Glucose Value.", "Fields_cannot_be_empty": "Fields can't be only dot or left blank.", "Please_select_test": "Please select test!", "Please_take_reading_again": "Please take reading again", "After_beepsound_please_Waitfor_2sec_tostart_reading": "After beep sound please Wait for 2 sec to start reading", "Reading_improper_please_take_again": "Reading is not taken properly. please take again.", "no_manual_entry_param": "no manual entry param", "HDL_Cholesterol": "HDL Cholesterol", "Total_Cholesterol": "Total Cholesterol", "GLUCOSE": "GLUCOSE", "BILIRUBIN": "BILIRUBIN", "SPECIFIC_GRAVITY": "SPECIFIC GRAVITY", "BLOOD": "BLOOD", "PROTEIN": "PROTEIN", "UROBILINOGEN": "UROBILINOGEN", "NITRITE": "NITRITE", "LEUKOCYTES": "LEUKOCYTES", "ReferenceStrip": "Reference Strip", "TestStrip": "Test Strip", "Result": "Result", "Processing": "Processing", "Initialize": "Initialize", "Click_the_Initialize": "Click the Initialize", "Check_referencestrip_in_tray": "Check the presence of reference strip in the tray", "Negative": "Negative", "Positive": "Positive", "Click_the_Start_Test_button": "Click the Start Test button", "Select_Test_from_dropdown": "Select Test from dropdown", "Switch_on_device": "Switch on the device", "Insert_tray_in_optical_device": "Insert tray in optical reader device", "Connect_optical_reader": "Connect the optical reader device with computer using USB cable", "Please_follow_steps": "Please follow the steps below", "AmplitudeBar": "Amplitude Bar", "Note": "Note", "Remedi2": {"0_not_running_refresh_page": "Remedi2.0 internal component is not running.Please refersh the page."}, "SaveResults": "Save Results", "Start": "Start", "StartTest": "Start Test", "HeartRate": "Heart Rate", "Record": "Record", "Discard": "Discard", "Next_Reading": "Next Reading", "Consultation_unmarked_as_followup": "Consultation unmarked as follow up.", "Consultation_marked_as_followup": "Consultation marked as follow up.", "Blood_And_Urine_Tests": "Blood And Urine Tests", "Foetal_Care": "Fetal Care", "ACR_Test": "ACR Test", "HbA1c": "HbA1c", "Lipid_Profile": "Lipid Profile", "Physiology_Tests": "Physiology Tests", "Ok": "Ok", "Problem_in_Changing_Password": "Problem in Changing Password.", "Changing_Password_Rule_Not_Matching": "The password must be at least 8 characters long and include a number, one special character, and an uppercase letter", "Old_Password_is_incorrect": "Old Password is incorrect.", "Password_has_been_changed_successfully": "Password has been changed successfully.", "Please_check_the_terms_and_conditions": "Please check the terms and conditions.", "terms_and_conditions": "terms and conditions.", "I_agree_with": "I agree with", "Patient_already_in_consultation_with_some_other_user": "Patient already in consultation with some other user", "edit": "edit", "Edit": "Edit", "Search_By_Finger_Print": "Search By Finger Print", "Print_Report": "Print Report", "Medicines_not_precribed_in_this_consultation": "Medicines not prescribed in this consultation!", "Select_User": "Select User", "Enter_Patient_Id": "Enter Patient Id", "Local_Consultation": "Local Consultation", "Remote_Consultation": "Remote Consultation", "Review_Cases": "Review Cases", "Slots_have_been_added_successfully": "Slots have been added successfully.", "Pharmacy_Configuration": "Pharmacy Configuration", "Pharmacy": "Pharmacy", "Please_Enter_Quantity": "Please provide Quantity", "Quantity": "Quantity", "ProjectId": "Project Id", "Username": "Username", "Password": "Password", "Login": "<PERSON><PERSON>", "Neurosynaptic": "Copyright", "nurseLogin": "nurse<PERSON>ogin", "Hello": "Hello", "Make_Appointments": "Make Appointments", "Register_New_Patient": "Register New Patient", "Or_Fix_Appointment": "Fix Appointment", "Choose_speciality": "Choose Speciality", "Fee_Range": "Fee Range", "Time_Range": "Time Range", "Find_Doctor": "Find <PERSON>", "Diagnostic_or_Patient_Records": "Diagnostic or Patient Records", "Or_Search_Patient": "Advanced Search", "Or_Register_Patient": "Register Patient", "Go_For_Diagnostic": "Go to Diagnostics", "Go_For_Print_Patient_ID_Card": "Print Patient ID Card", "View_Patient_Records": "Patient Records", "Show_Appointments": "Show Appointments", "Your_Appointments": "Your Appointments", "Patient_Image": "Patient Image", "Patient_Name": "Patient Name", "Start_Time": "Start Time", "End_Time": "End Time", "Appointment_Date": "Appointment Date", "Nurse_Name": "Nurse Name", "Status": "Status", "Action": "Action", "Parameter_Manual_Entry_Status": "Parameter Manual Entry", "Please_Provide_Patient_Id_first": "Please provide Patient ID.", "Please_Provide__Valid_Patient_Id": "Please provide a Valid Patient ID.", "Patient_is_not_registered": "Patient is not registered. Please check.", "No_Patient_Found": "No Patient Found", "No_Appointment_Found": "No Appointments Found", "Active": "Active", "Cancelled": "Cancelled", "Completed": "Completed", "Both_Active_and_Cancelled": "Both Active and Cancelled", "Suspended": "Suspended", "All": "All", "Pending": "Pending", "Reviewed": "Reviewed", "Search_Result": "Search Result", "Id": "ID", "Gender": "Gender", "Date_of_Birth": "Date of Birth", "Search_Doctor": "Search Doctor", "Fix_Appointment": "Fix Appointment", "Book": "Book", "Doctor_Name": "Doctor Name", "Degree": "Degree", "Available_Slots": "Available Slots", "From_Time": "From Time", "To_Time": "To Time", "Cancel_the_slots": "Cancel the slots", "Book_the_slots": "Book the slots", "Please_Select_Doctor_Name_First": "Please select Doctor Name.", "Please_Select_Current_Date_First": "Please select Current Date.", "You_cannot_select_the_date_less_than_current_date": "You can't select the date less than current date", "Please_Choose_slot_first": "Please <PERSON><PERSON> slot first", "Slot_has_already_been_taken_by_someone_else": "Slot has already been taken by someone else", "No_Slot_Available": "Slots are not available for the selected Doctor", "Something_went_wrong_Please_retry": "Something went wrong Please retry", "something_went_to_wrong_please_relogin": "Something went wrong. Please login again.", "Problem_in_starting_local_consultation": "Problem in starting local consultation", "You_cannot_start_cancelled_appointment": "You cannot start cancelled appointment", "OK": "OK", "Edit_the_slot": "Edit slot", "Appointment_rescheduled": "Appointment has been rescheduled", "search_Patient": "Search Patient", "Register_By_You": "Register By You", "Register_By_All": "Register By All", "From_Date": "From Date", "Till_Date": "To Date", "patient_first_Name": "First Name", "patient_Last_Name": "Last Name", "DOB": "DOB", "Male": "Male", "M": "Male", "m": "Male", "male": "Male", "Female": "Female", "female": "Female", "Other": "Other", "Age": "Age", "Address": "Address", "Search": "\tSearch", "Additional_Information": "Additional Information", "Please_Provide_Patient_First_Name_to_Search": "Please provide Patient First Name to search.", "Mobile_contact_number": "Mobile/Contact Number", "Married": "Married", "Unmarried": "Single", "Hieght": "Height", "Weight": "Weight", "Head_of_Household": "Contact Person", "Email": "Email", "Head_of_HouseHold_first_name": "Contact Person First Name", "Head_of_HouseHold_Last_name": "Contact Person Last Name", "UID": "UID", "Past_Records": "Past Records", "Diagnosis": "Diagnosis", "Update_Patient": "Update Patient", "Register_Patient": "Register Patient", "Select_country": "Select country", "Select_Block": "Select Block", "Select_Village": "Select Village", "Upload_Patient_Image": "Capture Patient Image", "Take_another": "Take another", "Please_Select_Country_First": "Please Select Country First", "Please_Select_State_First": "Please Select State First", "Please_Select_District_First": "Please Select District First", "Please_Select_location_in_order": "Please Select location in order", "Upload_Reports": "Upload Document/Image", "Upload_from_computer": "Browse Computer for Image", "Take_live_photo": "Capture Image", "Image_Type": "Image Type", "Document_Type": "Document Type", "Take_Another": "Take Another", "Register1": "Register", "please_share_camera_first_and_then_take_photo": "Please share camera to take photo.", "please_Browse_record_from_computer_OR_take_from_camera": "Please browse computer for image OR take an image using camera.", "Please_Provide_Patient_Last_Name": "Please provide Patient Last Name.", "Please_Provide_Patient_First_Name": "Please provide Patient First Name.", "Please_Provide_Patient_Age": "Please provide Patient Age.", "Please_Provide_Patient_Gender": "Please provide Patient Gender.", "Mobile_Number_Is_Invalid": "Please enter a valid Contact Number.", "Please_Choose_image_Type_for_record": "Please choose image type for record.", "Patient_is_registered_successfully_patient_Id": "Patient has been registered successfully. Patient ID is", "Patient_registration_is_failed": "Patient registration is failed", "Please_Provide_valid_DOB": "Please Provide valid DOB", "Invalid_Patient_First_Name": "Invalid First Name", "Invalid_Patient_Last_Name": "Invalid Last Name", "Invalid_Contact_First_Name": "Invalid Contact First Name", "Invalid_Contact_Last_Name": "Invalid Contact Last Name", "Past_Reports": "Past Reports", "New_Records": "New Records", "View_Consultations": "View Consultations", "Send_To_Doctor_for_Review": "Send to doctor for review", "Save_Report": "Save Report", "Comment": "Comment", "Save_Comment": "Save Comment", "Report_uploaded_successfully": "Report has been successfully uploaded.", "Report_upload_failed": "Report upload failed", "Cant_find_slot_please_try_again": "Cant find slot please try again", "Cant_find_patient_please_try_again": "Cant find patient please try again", "Cant_find_doctor_please_try_again": "Cant find doctor please try again", "You_are_going_to_fix_the_appointment_for_patient_name": "You are going to fix an appointment for patient name", "Appointment_is_fixed": "Appointment has been fixed.", "This_patient_already_have_an_appointment_with_the_same_doctor_for_same_day": "This patient already has an appointment with the selected doctor for the same day.", "Do_you_want_to_continue": "Do you want to continue?", "Problem_in_fixing_appointment": "Problem in fixing appointment", "Appointment_Action": "Appointment Action", "Start_Consultation": "Start Consultation", "Or_Cancel_Appointment": "<PERSON>cel Appointment", "Local_Appointment_Action": "Local Appointment Action", "Start_Local_Consultation": "Start Local Consultation", "Problem_in_starting_consultation": "Problem in starting consultation", "You_cannot_start_canceled_appointment": "You cannot start Cancelled Appointment.", "You_cannot_start_Completed_appointment": "You cannot start Completed appointment.", "Something_went_wrong_local_consultaion_Please_relogin": "Something went wrong local consultaion Please relogin", "You_cannot_cancel_active_appointment": "You cannot cancel Active Appointment.", "No_Doctor_Available": "No Doctor Available", "Choose_Date": "<PERSON>ose <PERSON>", "Diognostic": "Diognostic", "Home": "Home", "Suspend": "Suspend", "Suspend_Consultation": "Suspend Consultation", "Patient_Historty": "Patient History", "complaints": "<PERSON><PERSON><PERSON><PERSON>", "Past_Report": "Past Report", "Parameters": "Parameters", "medication": "Medicines", "diagnosis": "Diagnosis", "Investigations": "Investigations", "Instructions": "Counseling", "ABDM_HIU": "ABDM HIU", "Recent_complaints_(_click_on_complaint_name_to_add_)": "Recent complaints.(Click on Complaint Name to Add)", "History_Of_Present_Illness": "\tHistory Of Present Illness", "Past_Medical_or_Surgical_History": "Past Medical or Surgical History", "Current_And_Recent_Medications": "Current And Recent Medications", "Other_Allergies_Or_Sensitivities": "Other Allergies Or Sensitivities", "Physical_Examination": "Physical Examination", "Personal_History": "Personal History", "Family_History": "Family History", "Medical_Allergies": "Medical Allergies", "Past_Consultation": "Past Consultation", "Past_Parameter": "Past Parameter", "upload_Report": "upload Report", "Temperature": "Temperature", "Spo2": "SpO2", "PastRecord_ManualEntry_Msg": "Graph Cann't be displayed for Manual Entry", "Glucose": "Glucose", "Cholesterol": "Cholesterol", "Hemoglobin": "Hemoglobin", "Systolic": "Systolic", "Diastolic": "Diastolic", "Pulse": "Pulse", "Spo2_Percentage": "Spo2 Percentage", "Spo2_Pulse": "Spo2 Pulse", "Percentage": "Percentage", "Pulse_rate": "Pulse rate", "View_Consultation": "View Consultation", "Follow_up": "Follow up", "Finish_Consultation": "Finish Consultation", "Sucessfully_uploaded": "Successfully uploaded", "Print_Email_Consultation": "Please <PERSON><PERSON> Required Actions Before Finishing Consultation", "pgender": "Gender", "Connect_to_doctor": "Connect to doctor", "Back_To_Home": "Back", "Cancel_Appointment": "Cancel appointment", "Problem_while_finish_consultation": "Problem while finish consultation", "This_consultation_cant_be_restart_do_you_want_to_continue": "You can restart this consultation from the Walk-in option.", "PRMS": "PRMS", "Options": "Options", "My_Calendar": "My Calendar", "You_have_to_find_doctor_first_to_fix_appointment": "You have to find doctor first to fix appointment", "configured_Slots": "Configured Slots", "Date1": "Date", "Duration": "Duration", "Till_Time": "To Time", "Allow_General_Patients": "Allow General Patients", "Add_Slots": "Add slots", "Please_Choose_Till_Date_Greater_than_From_Date": "Please Choose Till Date Greater than From Date", "Please_Choose_From_Date": "Please Choose From Date.", "Please_Choose_Till_Date": "Please choose To date.", "Please_Choose_From_Time": "Please choose From time.", "Please_Choose_Till_Time": "Please choose To time.", "Please_Choose_Till_Time_Greater_than_From_Time": "Please Choose Till Time Greater than From Time", "Please_Choose_Duration": "Please Choose Duration", "Are_you_sure_to_delete_these_slots": "Are you sure, you want to delete these slots?", "There_are_some_active_appointments_Are_you_sure_to_delete": "Appointments have been fixed in these slots. Are you sure, you want to delete these slots?", "Slots_cannot_be_deleted": "Slots cannot be deleted", "You_cannot_cancel_these_slots_nurse_is_waiting": "You cannot cancel these slots as nurse is waiting to connect.", "You_can_start_only_active_appointments": "You can start only active appointments", "Problem_in_cancelling_consultation": "Problem in cancelling consultation", "You_cannot_cancel_an_active_appointment": "You cannot cancel an active appointment", "You_can_start_only_active_or_suspended_appointments": "You can start only active or suspended appointments", "Remedi": "<PERSON><PERSON><PERSON>", "Logout": "Logout", "Change_Password": "Change Password", "Enter_Old_Password": "Enter Old Password", "Enter_New_Password": "Enter New Password", "Confirm_New_Password": "Confirm New Password", "Please_Provide_Complete_Information": "Please provide complete information.", "New_Password_cannot_be_same_as_old": "New password cannot be same as the old password.", "Confirm_password_is_not_matched_with_new_password": "Passwords do not match. Please try again.", "Request_Coupons": "Coupon", "Coupon_count": "Enter the number of Coupons", "Request": "Request", "Click": "Click here to get available coupons", "Please_Contact_Admin": "Contact Admin for Coupons", "Change_Language": "Change Language", "My_Profile": "My Profile", "New_Password_should_be_six_character_long": "New Password should be at least six character long.", "Hello_Admin": "Hello Admin", "Profile_Management": "Profile Management", "Configuration": "Configuration", "Report": "Report", "Switch_Video": "Switch Video", "Save_photo": "Save photo", "Doctor": "Doctor", "referrals": "Referrals", "Referral_Name": "Specialization", "complaint_name": "complaint name", "Drug_Class": "Drug Class", "medication_Brand": "medication Brand", "Drug_Form": "Drug Form", "Instruction": "Instructions", "Instruction_Category": "Instruction Category", "Instruction_Name": "Instruction Name", "ICD_Code": "ICD Code", "Diagnosis_Name": "Diagnosis Name", "Category_Name": "Category Name", "Category": "Category", "Investigation": "Investigation", "Test_Code": "Test Code", "lab_Name": "Lab Name", "Location": "Location", "Switch_to_low_bandwidth": "Switch To Low Bandwidth", "High_Bandwidth_video_mode_is_on": "High Bandwidth Video Conferencing has been switched ON", "Low_Bandwidth_video_mode_is_on": "Low Bandwidth Video Conferencing has been switched ON", "Switch_to_High_Bandwidth": "Switch To High Bandwidth", "Problem_in_switching_video_mode_Please_try_again": "Problem in switching video mode Please try again", "Something_went_wrong_Please_try_again": "Something went wrong Please try again", "User_Name": "User Name", "User_Roles": "User Roles", "Domains": "Domains", "Resources": "Resources", "Role_Mapping": "Role Mapping", "Select_Domain": "Select Domain", "Select": "Select", "Select_Blood_Sugar_Test": "Select Blood Sugar Test", "Resource_Mapping": "Resource Mapping", "Select_Role": "Select Role", "Edit_Profile": "Edit Profile", "Super_Admin": "Super Admin", "Parameter_Mapping": "Parameter Mapping", "User_LoggedOut": "User LoggedOut", "Doctor_Domain_Configuration": "Doctor Domain Configuration", "adminLogin": "adminLogin", "Profile": "Profile", "Honorific": "Honorific", "Name": "Name", "State": "State", "District": "District", "Block": "Block", "Village": "Village", "niramaiUserName": "<PERSON><PERSON><PERSON> UserName", "niramaiPassword": "Niramai Password", "cancer": "Cancer", "Patient": "Patient", "Remedi_Parameter": "<PERSON><PERSON>i Parameter", "Other_Parameter": "Other Parameter", "Complaints_Data": "Complaints Data", "ID": "ID", "status": "Status", "ok": "ok", "Domain": "Domain", "Resource": "Resource", "Role": "Role", "Restart_Video": "Restart video", "Doctor_Information": "Doctor Information", "Nurse_Information": "Nurse Information", "Patient_Information": "Patient Information", "Patient_history": "Patient History", "Please_Provide_Username": "Please  Provide  <PERSON>rname", "Please_Provide_Password": "Please  Provide  Password", "Another_User_already_logged_in_Retry": "Another%20User%20already%20logged%20in%20Retry", "User_already_logged_in_Retry": "User%20seems%20to%20be%20already%20logged%20in.%20Please%20try%20again%20after%20some%20time.", "Something_wrong_Please_try_again": "Something%20went%20wrong%20Please%20try%20again", "Domain_is_not_registered": "Domain is not registered", "Unauthorized_Access_Retry": "Unauthorized%20Access%20Retry", "invalid_Username_Retry": "invalid%20Username%20Retry", "Invalid_Password_Retry": "Invalid%20Password%20Retry", "Problem_in_Login_Retry": "Problem%20in%20Login%20Retry", "You_dont_have_permission_to_login_Retry": "You%20dont%20have%20permission%20to%20login%20Retry", "Invalid_Username_or_Password_Retry": "Invalid%20Username%20or%20Password%20Retry", "Problem_in_Location_Center": "Can't%20login%20as%20location%20or%20center%20for%20this%20nurse%20is%20unassigned", "ReMeDi_Parameters": "ReMeDi Parameters", "Stethoscope": "Stethoscope", "StethoscopeBT": "StethoscopeBT", "ECG": "Electro Cardio Gram", "Dermatoscope": "Dermatoscope", "Autoscope": "Autoscope", "Other_Parameters": "Other Parameters", "Rapidtest": "Optical Reader", "Glucose_Result": "Glucose Result", "Cholesterol_Result": "Cholesterol Result", "Enter_3_digit_Batch_No": "1. Enter the 3-digit batch code from the strip bottle.", "Ensure_the_same_code_is_entered_on_the_device": "2. Ensure the same code is entered on the device.", "If_want_to_update": "If want to update", "Take_Reading": "Take Reading", "Hemoglobin_Result": "Hemoglobin Result", "Please_Select_Test": "Please Select Test", "DENGUE": "DENGUE", "HCG": "HCG", "HEPA_A": "HEPA A", "HEPA_B": "HEPA B", "HIV": "HIV", "MALARIA": "MALARIA", "SYPHILIS": "Syphilis", "URINE_CHECK": "URINE CHECK", "And_press": "And press", "Show_result": "Show result", "Edit_Patient": "<PERSON>", "physical_Examination": "Physical Examination", "Search_Past_Consultations": "Search Past Consultations", "RapidTest": "RapidTest", "parameteres_not_taken_for_this_patient": "Vitals not taken in this consultation!", "may_be_spo2_reading_not_taken": "May be SpO2 reading not taken", "incomplete_data": "Incomplete data!", "data_not_found": "Data not found!", "may_be_ecg_reading_not_taken": "may be ecg reading not taken", "may_be_stetho_reading_not_taken": "may be stetho reading not taken", "please_enter_3_digit_number": "please enter 3 digit number", "May_be_device_is_OFF_or_NOT_CONNECTED_please_try_again": "Please check if the device has been connected properly.", "error": "error", "Please_put_finger_into_SPO2_device_And_Retry": "Please put finger into SpO2 device And Retry", "Either_SPO2_probe_is_not_connected_OR_Dead_object_detected_Please_Retry": "Please check if the SpO2 probe is connected and the finger has been inserted properly.", "Please_connect_SPO2_probe_And_Retry": "Please connect SpO2 probe And retry", "stop": "stop", "Please_Manually_Reset_the_kit_and_click_on_OK_button": "Please manually reset the kit and click on OK button.", "Unable_to_connect_ReMeDi_Kit_Please_make_sure": "Unable to start measurement. Please make sure that", "#In_case_of_USB_ReMeDi_kit_please_enusre_that_it_is_switched_ON": "In case of USB ReMeDi kit, please enusre that it is switched ON.", "ReMeDi_kit_is_connected_properly": "ReMeDi Kit and/or Bluetooth Dongle is connected properly", "In_case_of_USB_ReMeDi_kit_please_enusre_that_it_is_switched_ON": "The device in use should be switched ON.", "Jungo_driver_for_ReMeDi_is_installed_enabled": "Driver for ReMeDi Kit and Bluetooth Dongle has been installed and is enabled.", "Click_on_OK_once_the_above_mentioned_steps_are_done": "Click on Connect once the above mentioned steps are done", "Parameter_Window_Unloaded_Please_Refresh_page_and_try_again": "Parameter Window Unloaded Please Refresh page and try again", "Levels": "Levels", "Label_for_Level": "Label for Level", "Please_Enter_Labels_For_All_The_Levels": "Please Enter Labels For All The Levels", "Please_Select_Levels": "Please Select Levels", "Please_select_levels_in_location_hierarchy": "Please select levels in location hierarchy.", "Search_Consultations": "Search Consultations", "Please_Relogin": "Please Relogin", "error1000": "Parameter didn't get started, Please Refresh the page", "error1001": "Socket Connection error; Please Retry", "error1002": "Please connect the temperature Probe", "Temp_In_Fahrenheit": "Temperature (Fahrenheit)", "ARC": "ACR", "ALB": "Albumin", "Creatinine": "Creatinine", "error1003": "Please make sure that the patient does not have thin arms.<br>Or Check if the BP cuff is tied properly to the arms.<br>Or else contact the technical support team.", "error1004": "Please make sure that the patient does not have thin arms.<br>Or Check if the BP cuff is tied properly to the arms.<br>Or else contact the technical support team", "error1005": "Please take another reading as the patient seems to be hypertensive.<br>Or Check if the BP cuff is tied properly to the arms.", "error1006": "Please take another reading.<br>While taking BP reading, the patient should not move.<br>Or Check if the BP cuff is tied properly to the arms.", "error1007": "Please restart the system and take another reading", "Investigation_Data": "Investigation Data", "Instruction_Data": "Instruction Data", "Referral_Data": "Referral Data", "Resource_ID": "Resource ID", "Country_Code": "Country Code", "Village_ID": "Village ID", "Enable_selected": "Enable Selected", "Disable_Selected": "Disable Selected", "Consultation_Report": "Consultation Report", "Foot_Secure_Report": "Foot Secure Report", "Dr": "Dr", "Parameters_Readings": "Parameter Readings", "Foot_Report": "Foot Report", "Foot_Examination": "Foot Examination", "Foot_Results": "Results", "BMI": "Body Mass Index (BMI)", "Mandatory": "Mandatory", "Optional": "Optional", "Medicines": "Medicines", "SrNo": "SrNo", "Special_Instruction": "Special Instruction", "Take_Photo": "Take Photo", "Currency_Number_Mapping": "Choose Currency and Length of Mobile Number", "Choose_Currency": "<PERSON><PERSON>", "Currency_Sybbol": "Currency Symbol", "Phone_Number_Length": "Phone Number Length", "Please_Provide_Valid_Phone_length": "Please provide valid length for phone number.", "Currency_Updated": "Currency and length of phone number has been updated.", "Problem_IN_Updating_Currency": "Problem in updating currency", "error1008": "Device not connected", "error1009": "Device is OFF", "error1010": "Please insert the Strip", "error1011": "Please put blood on the Strip", "error1012": "Please Wait for Result", "Please_Wait_preparare_past_record": "Please wait while preparing past record", "error1013": "May be device is OFF or not connected properly", "error1014": "Please check if the Temperature probe is connected properly.", "error1020": "Batch updated, now please insert the strip", "error1021": "Ecg Reading Not complete, please take another reading", "IsDeleted": "Disabled", "Patient_is_successfully_updated": "Patient record has been successfully updated.", "Patient_updation_is_failed": "Patient updation is failed", "For_Manual_Entry": "For Manual Entry", "Enter_Hemoglobin_Result": "Enter Hemoglobin Result", "Save_Result": "Save Result", "Please_enter_result": "Please enter result of Hemoglobin test.", "Result_saved_successfully": "Result saved successfully", "referral": "Referral", "fetal_dopler": "<PERSON><PERSON>", "spiro": "Spirometer", "Free_Text": "Free Text", "Yes": "Yes", "No": {"of_Prescription": "No.of Prescription", "_of_Prescription": "No. of Prescription"}, "Add_Medication_Brand": "Add Medicine Brand", "Add_Drug_Form": "Add Drug Form", "Add_Drug_Class": "Add Drug Class", "Are_You_Sure": "Are you sure?", "pressure": "Pressure", "Add_Category": "Add Category", "Add_Lab_Name": "Add Lab Name", "Please_Wait": "Please Wait", "Data_Saved": "Data has been saved.", "Error_while_saving": "Error while Saving", "this_medicine_already_prescribed": "This medicine has been already prescribed.", "problem_occured_while_inserting_medicine": "problem occured while inserting medicine", "problem_occured_while_updating_medicine": "problem occured while updating medicine", "problem_occured_while_deleteing_medicine": "problem occured while deleteing medicine", "Celsius": "�C", "Height": "Height", "Glu": "Glu", "chol": "Chol", "hemo": "<PERSON><PERSON>", "Temp": "Temp", "BP_sys": "BP Sys", "dis": "BP Dia", "Lipid": "Lipid", "F200": "F200 Analyzer", "Fields_are_Mandatory": "Fields are mandatory", "Upload_Image": "Upload Image", "Consultation_Reports": "Consultation Reports", "Medical_Images": "Medical Images", "Vitals": "Vitals & Reports", "Generate_Reports": "Generate Reports", "Export_Reports_Between": "Export Reports Between", "separated": "Separated", "widow": "Widow", "Please_select_or_enter_instruction": "Please select or enter Recommendation", "Please_enter_instruction": "Please enter recommendation", "instruction_is_too_long": "Recommendation is too long", "Please_select_or_enter_test": "Please select or enter Lab Test.", "Please_enter_test": "Please enter test", "test_name_is_too_long": "Test name is too long", "Please_select_or_enter_diagnosis": "Please select or enter Diagnosis.", "Please_enter_both_icdcode_and_diagnosis": "Please enter ICD Code and Diagnosis.", "Both_AutoSelected_Diagnosis_FreeText_cannot_choose": "Both Auto-Selected Diagnosis and Free Text cannot be chosen.", "diagnosis_is_too_long": "Diagnosis is too long", "Total_Time_of_Usage": "Total Time of Usage", "Doctor_Wise": "Doctor <PERSON>", "Nurse_Wise": "Nurse Wise", "Doctor_wise_Time_Report": "Doctor wise Time Report", "Nurse_wise_Time_Report": "Nurse wise Time Report", "Total_Time": "Total Time", "No_Data_Found": "No Data Found", "error1022": "Device is ON", "Others": "Others", "All_Green_Color_Fields_are_Mandatory": "All green color fields are mandatory", "You_cannot_cancel_completed_appointment": "You cannot cancel completed appointment", "This_appointment_is_already_cancelled": "This appointment is already cancelled", "problem_occurred_while_canceling_consultation": "problem occurred while canceling consultation", "Generated_by": "Generated by", "This_is_an_electronically_generated_report": "This is an electronically-generated report.", "SpO2": "SpO2", "BP": "BP", "GlucoseRandom": "Glucose Random", "GlucoseFasting": "Glucose Fasting", "GlucosePostprandial": "Glucose PostPrandial", "Chol": "Chol", "Hemo": "<PERSON><PERSON>", "Consultation_is_still_happening_Please_finish_that_first": "Consultation is still happening. Please finish that first.", "Your_Domain_has_been_Expired_Please_Renew": "You%20cannot%20access%20the%20server.%20Please%20contact%20your%20administrator%20to%20reactivate%20the%20access.", "Your_License_has_been_Expired_Please_Renew": "Your%20license%20has%20expired.%20Please%20contact%20your%20administrator%20to%20renew%20license.", "Your_Domain_has_been_Expired_Please_Renew_Android": "You can't access the server. Please contact your administrator to reactivate the access.", "Your_License_has_been_Expired_Please_Renew_Android": "Your license has expired. Please contact your administrator to renew license.", "remedi_server_not_running": "Remedi2.0%20internal%20component%20is%20not%20running.Please%20start%20and%20refersh%20the%20page.", "Please_put_finger_into_Spo2_sensor": "Please put finger into Spo2 sensor.", "Dead_object_detected": "Dead object detected.", "Sensor_switched_off_Please_take_reading_again": "Sen<PERSON> switched off! Please take reading again.", "CCA_Test": "Clinical Chemistry Tests", "Haematology_Analyzer": "Haematology Tests", "Glucose_POC": "Glucose(POC)", "Lipid_Profile_POC": "Lipid Profile(POC)", "Slot_is_Updated_Successfully": "Slot is updated successfully", "Slots_have_been_updated_successfully": "Slots have been updated successfully", "Delete": "Delete", "0": 0, "1": 1, "2": 2, "3": 3, "Body_Composition": "Body Composition", "Body_Weight": "Body Weight", "Body_Height": "Body Height", "Body_Fat": "Body Fat", "Visceral_Fat": "Visceral Fat", "Skeletal_Muscle": "Skeletal Muscle", "Body_Age": "Body Age", "Resting_Metabolism": "Resting Metabolism", "BodyComposition_ManualEntry": "Body Composition Entry", "Successfully_saved_BodyComposition_data": "Successfully saved Body Composition data", "Total_CRP": "CRP", "HbA1C": "HbA1C", "U-ALB": "&micro;-ALB", "Enter_Valid_RCP_reading": "Enter valid RCP_reading", "Enter_Valid_HBA1C_reading": "Enter valid HbA1c reading", "Enter_Valid_UALB_reading": "Enter valid &micro;-ALB reading", "Value_of_HbA1C": "HbA1C", "Total_U-ALB": "Microalbumin(&micro;-ALB)", "Multicare_ManualEntry": "F200 analyzer ManualEntry", "Successfully_saved_Mutlicare_data": "Data saved successfully", "MulticareProfile": "F200 analyzer Profile", "Problem_occured_while_updating_domain_details": "Problem occured while updating domain details", "No_record_found": "No record found", "Error_while_logging_out": "Error while logging out", "Total_Record_Count": "Total Record Count", "Ignore": "Ignore", "Data_not_found": "Data not found", "Value": "Value", "Patient_already_registered": "Patient already registered", "Slots_not_available": "Slots not available", "Complete": "Complete", "In_Review": "In Review", "Done": "Done", "unique_PID": "uniquePID", "Slot_time_duration_is_overlapping_with_another_appointment_Please_try_different_slot": "Slot time duration is overlapping with another appointment. Please try different slot", "Appointment_updated_successfully": "Appointment updated successfully", "Appointment_does_not_exist_Please_create_new_appointment_first": "Appointment does not exist. Please create new appointment first", "Appointment_can_be_rescheduled_15_minutes_prior_to_scheduled_time": "Appointment can be rescheduled 15 minutes prior to scheduled time.", "pat_Name": "<PERSON><PERSON><PERSON>", "Inqueue": "Inqueue", "Connected": "Connected", "Fail": "Fail", "lease_provide_consultation_ID": "Please provide consultation ID", "Incorrect_domain": "Incorrect domain", "Please_provide_created_date": "Please provide created date", "Appointment_is_cancelled": "Appointment is cancelled", "Appointment_is_cancelled_It_will_take_6-7_working_days_to_refund": "Appointment is cancelled. It will take 6-7 working days to refund", "Error_while_inserting_records_in_Database": "Error while inserting records in Database", "REFUND_FAILED": "REFUND FAILED", "Invalid_domain": "Invalid domain", "Error_while_processing_refund_Please_connect_to_hospital's_admin": "Error while processing refund. Please connect to hospital's admin", "Error_while_processing_refund": "Error while processing refund", "Exception_while_refunding_payment_transaction": "Exception while refunding payment transaction", "Appointment_can_be_cancelled_15_minutes_prior_to_scheduled_time": "Appointment can be cancelled  15 minutes prior to scheduled time.", "Screening": "Screening", "reviewed_by_doctor": "reviewed by doctor", "is_FollowUp_Val": "isFollowUpVal", "Cannot_make_connection": "Cannot make connection", "Cannot_check_meeting": "Cannot check meeting", "Cannot_end_meeting": "Cannot end meeting", "Consult_Fee_To_be_Return": "Consult Fee To be Return", "Problem_occured_while_updating_nurse_profile": "Problem occured while updating nurse profile", "Device_ID_is_NULL": "Device ID is NULL", "Error_while_accessing_Client_requests_OAuth_Login_URL's": "Error while accessing Client requests OAuth Login URL's", "oauth_Url_is_NULL": "oauth <PERSON> is NULL", "Authentication_failed": "Authentication failed", "Please_try_after_some_time": "Please try after some time.", "Invalid_credentials": "Invalid credentials", "Inserted_successfully": "Inser<PERSON> successfully", "Failure_to_insert": "Failure_to_insert", "Exception_while_processing_Stethoscope_data": "Exception while processing Stethoscope data", "patient_Info": "patient Info", "Exception_in_recording_login_time": "Exception in recording login time", "User_seems_to_be_already_logged_in_Please_try_again_after_some_time": "User seems to be already logged in. Please try again after some time.", "Incorrect_username_or_password": "Incorrect username or password", "Invalid_Domain": "Invalid Domain", "Successfully_logged_out": "Successfully logged out", "Password_changed_successfully": "Password changed successfully", "Error_while_changing_password": "Error while changing password", "Error_while_getting_URL_from_S3": "Error while getting URL from S3", "Invalid_domain_name": "Invalid domain name", "Parameter_not_enabled_for_this_domain": "Parameter not enabled for this domain", "Please_provide_valid_consultationId_Or_username": "Please provide valid consultationId Or username", "Patient_Not_Exist_For_Given_Mobile": "Patient Not Exist For Given Mobile", "No_patient_exists_with_given_mobile_number": "No patient exists with given mobile number", "password_Exists": "password Exists", "Please_provide_password_to_login": "Please provide password to login", "Error": "Error", "same_As_Old_Password": "sameAsOldPassword", "New_password_cannot_be_same_as_old_password": "New password cannot be same as old password", "Please_provide_all_parameters": "Please provide all parameters", "Exception_while_inserting_payment_status": "Exception while inserting payment status", "Payment_Gateway_status_inserted_succesfully": "Payment Gateway status inserted succesfully", "Error_occurred_to_insert_data_in_Payment_Gateway_status": "Error occurred to insert data in Payment Gateway status", "store_id": "store id", "Error_occurred_to_get_Payment_Gateway_details": "Error occurred to get Payment Gateway details", "Exception_while_creating_payment_order": "Exception while creating payment order", "Please_provide_payment_status": "Please provide payment status", "Booked": "Booked", "Error_while_updating_payment_status_in_database": "Error while updating payment status in database", "Error_while_cancelling_consultation_ID": "Error while cancelling consultation ID", "Nurse_profile": "Nurse profile", "patient_Profile": "patientProfile", "Error_while_updating_Corona_Symptoms_Details": "Error while updating Corona Symptoms Details", "Error_while_updating_Corona_Travel_History": "Error while updating Corona Travel History", "Error_while_updating_Corona_Test_Details": "Error while updating Corona Test Details", "Error_while_updating_Corona_Medical_History": "Error while updating Corona Medical History", "Invalid_action": "Invalid action", "NURSE_PATIENT_MAPPING": "Nurse <PERSON>ient Mapping", "Select_Patient": "Select Patient", "Assign": "Assign", "Fasting": "Fasting", "Random": "Random", "Postprandial": "Postprandial", "Foot_analyzer": "<PERSON> Analyzer", "Breast_Cancer_Screening": "Breast Cancer Screening", "Please_upload_pdf_report": "Please upload pdf report", "Required_data_not_provided": "\"{\\\"status\\\"", "HEPA_SCAN_HBsAg": "Hepa Scan HBsAg", "HEPA_SCAN_HCV": "Hepa Scan HCV", "TROPONIN_I": "Troponin I", "MALERIA_P": {"f_P": {"v": "Malaria P.f/P.v"}, "f_PAN": "Malaria P.f/Pan", "f_Antigen": "Malaria P.f Antigen", "f": "Malaria P.f"}, "DENGUE_NS1": "Dengue NS1", "DENGUE_IgG_IgM": "Dengue IgG/IgM", "DENGUE_IgG_IgM(Bhat_Biotech)": "Dengue IgG/IgM (BHAT BIO-TECH)", "DENGUE_IgG_IgM(SD_Standard)": "Dengue IgG/IgM (SD STANDARD)", "PREGNANCY_HCG": "Pregnancy hCG", "HIV_TRILINE": "HIV Triline", "HIV_I": "HIV I", "HIV_II": "HIV II", "Maleria_P": {"f": "Malaria P.f", "v": "Malaria P.v"}, "MALERIA_PAN": "Malaria Pan", "Do_you_want_to_reschedule_the_appointment": "Do you want to reschedule the appointment", "Appointment_can_be_rescheduled_prior_15_minutes_to_scheduled_time": "Appointment can be rescheduled prior 15 minutes to scheduled time.", "Appointment_can_be_cancelled_prior_15_minutes_to_scheduled_time": "Appointment can be cancelled prior 15 minutes to scheduled time.", "other": "Other", "homme": "Male", "Doctors": "Doctors", "AGENT": "AGENT", "Appointment_is_Removed": "Appointment has been closed", "Problem_in_Closing_Appt": "Problem in closing appointment", "You_cannot_start_done_appointment": "You Can Not Start Done Appointment", "Please_SwitchOn_NeuroTouch_Device": "Please switch on the NeuroTouch device", "Yostra_Data_Sent_Success_Message": "Data has been sent successfully", "View_Cancer_Report": "View Cancer Report", "PROJECT_NAME": "Project Name", "PROJECT_LOCATION": "Project Location", "project_location_title": "Project Center Mapping", "subscription": "Subscription Configuration", "subscription_config": "Add Subscription Plan for Family", "familycardid": "Unique Id", "row_familycardid": "FamilyCard Id", "Select_Subscription": "Select Subscription", "Please_Provide_Valid_FamilyCardId": "Please Provide Valid Unique Id", "subcriptionId": "Subscription Id", "subcription_name": "Subscription Name", "no_active_subcriptione": "No Active Subscription", "completed_free_subcriptione": "Completed Free Subscription", "remainig_subcription": "Remaining Free Subscription", "active_subcription": "Active Subscription", "startdate": "Start Date", "enddate": "End Date", "subcription_free": "Free Subscription", "subcription_activate": "Activate", "family_not_registered": "Family is not registered. Please check.", "subcription_expired": "Expired", "startdate_enddate_check": "End date must be greater than or equal to start date.", "please_select_subscription_plan": "Please select Subscription Plan", "plan_already_expired": "Plan has already expired", "subscription_expire_on": "Subscription Expire On", "subscription_activated_on": "Subscription Activated On", "subscription_activate_from": "This Subscription plan activate from Start Date", "subscription_already_activation_notification": "The selected subscription plan is currently active for the Family group. Are you sure you want to deactivate it?", "click_here_to_subscribe": "Click here to Subscribe", "click_here_to_reactivate_subscribe": "Click here to Reactivate subscription", "project_location_heading": "Location", "project_center": "Center", "project_center_status": "Center Status", "please_select_project_location": "Please Select Location", "please_select_center": "Please Select Center", "select_location": "Select Location", "project_center_id": "centerId", "Error_message_center_disabled": "Please%20Contact%20Administrator", "Select_Dose": "Select Dose", "Select_Frequency": "Select Frequency", "param_BloodPressure": "BloodPressure", "param_Blood_Pressure": "Blood Pressure", "param_Electrocardiogram": "Electrocardiogram", "param_PulseOximeter": "PulseOximeter", "param_Pulse_Oximeter": "Pulse Oximeter", "param_SpiroMeter": "SpiroMeter", "param_Spiro_Meter": "<PERSON><PERSON><PERSON>", "param_StethoScope": "StethoScope", "param_Stetho_Scope": "<PERSON><PERSON><PERSON>", "param_Temperature": "Temperature", "param_Glucose": "Random Blood Sugar", "param_Fasting_Blood_Sugar": "Fasting Blood Sugar", "param_Post-prandial_Blood_Sugar": "Post-prandial Blood Sugar", "param_Hemoglobin": "Hemoglobin", "param_Lipid": "Lipid", "param_Rapidtest": "Rapidtest", "param_Urinetest": "Urinetest", "param_FetalDopler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "param_HBA1C": "HbA1c", "param_Hba1c": "HbA1c", "param_ARC": "ARC", "param_ACR": "ACR", "param_PatientMonitor": "PatientMonitor", "param_Dermatoscope": "Dermatoscope", "param_Otoscope": "Otoscope", "param_Ultrasound": "Ultrasound", "param_CCA_Test": "CCA Test", "param_Haematology_Analyzer": "Haematology Analyzer", "param_ClinicalChemistryTests": "ClinicalChemistryTests", "param_Clinical_Chemistry_Tests": "Clinical Chemistry Tests", "param_HaematologyTests": "HaematologyTests", "param_Haematology_Tests": "Haematology Tests", "param_BodyComposition": "BodyComposition", "param_Body_Composition": "Body Composition", "param_F200Analyzer": "F200Analyzer", "param_FootAnalysisReport": "FootAnalysisReport", "param_BreastCancerScreening": "BreastCancerScreening", "param_Breast_Cancer_Screening": "Breast Cancer Screening", "param_CRP": "CRP", "param_microalbumin": "microalbumin", "param_Respiratory": "Respiratory", "Installer": "Installer", "Disable": "Disable", "Enable": "Enable", "Monthly_Report": "Monthly Report", "Select_Year": "Select Year", "Select_Month": "Select Month", "January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December", "Generate_Stock": "Generate Stock", "Cancer": "Cancer", "Name/Username": "Name/Username", "Please_select_Year_and_Month": "Please select Year and Month", "Choose_File": "Choose <PERSON>", "Export_to_excel": "Export to excel", "Dashboard": "MIS Dashboard", "Total_Consultation": "Total Consultation", "DoctorLocal": "Doctor", "DoctorRemote": "Doctor", "NurseLocal": "Nurse / Patient", "NurseRemote": "Nurse / Patient", "Local": "Local", "Remote": "Remote", "Patient_ID": "Patient identification Card", "Registrations": "Registrations", "Top_diagnosis": "Top diagnosis", "Collapse": "Collapse", "Remove": "Remove", "top_investigations": "top investigations", "Loading": "Loading", "Patient_Profile": "Patient Profile", "Sensor_Usage": "Sensor <PERSON>age", "Top_ten_medicine": "Top ten medicine", "Log": "Log", "Out": "Out", "Are_you_sure_you_want_to_log_out?": "Are you sure you want to log out?", "Press_No_if_you_want_to_continue_work": {"_Press_Yes_to_logout_current_user": {"": "Press No if you want to continue work. Press Yes to logout current user."}}, "MIS_Report": "MIS Report", "Detail_Consultation_Report": "Detail Consultation Report", "Consultation_Summary": "Consultation Summary", "Export_Data": "Export Data", "Age_Filter": "Age Filter", "Select_Age_Range": "Select Age Range", "Select_Duration_range": "Select Duration Range", "Save_changes": "Save changes", "Patient_Id": "Patient Id", "Visit": "Visit", "Sex": "Sex", "Date": "Date", "Speciality": "Speciality", "Aayushman_Bharat": "<PERSON><PERSON><PERSON><PERSON>", "Aayushman_Bharat_Survey": "<PERSON><PERSON><PERSON><PERSON>t <PERSON>", "Aayushman_Bharat_Survey_Details": "<PERSON><PERSON><PERSON>man Bharat Survey Details", "Name_OF_Patient": "Name OF Patient", "ID_No": "ID_No", "Date_of_Examination": "Date of Examination", "Is_the_Patient_eligible_for_Aayushman_Bharat_Golden_Card?": "Is the Patient eligible for <PERSON><PERSON><PERSON>man <PERSON>t Golden Card?", "Do_you_have_a_Aayushman_Bharat_Golden_Card?": "Do you have a <PERSON><PERSON>ushman Bharat Golden Card?", "Have_you_ever_used_Aayushman_Bharat_Golden_Card?": "Have you ever used <PERSON><PERSON><PERSON>man Bharat Golden Card?", "Billing": "Billing", "Doctor_Username": "Doctor <PERSON><PERSON><PERSON>", "Total_Test_Wise_Fees": "Total Test Wise Fees", "Line_Chart": "Line Chart", "Total_Fees": "Total Fees", "Consultation_Fees": "Consultation Fees", "Diagnosis_Fees": "Diagnosis Fees", "Bill_Details": "<PERSON>", "Foot_Analysis_Report": "Foot Analysis Report", "Microalbumin": "microalbumin", "CRP": "CRP", "Labtest": "Labtest", "ACR_Fees": "ACR Fees", "HbA1c_Fees": "HbA1c Fees", "Fetal_Fees": "<PERSON><PERSON>", "UrineTest_Fees": "UrineTest Fees", "Rapid_Fees": "Rapid Fees", "Lipid_Fees": "Li<PERSON>", "Hemoglobine_Fees": "Hemoglobine <PERSON>es", "Glucose_Fees": "Glucose Fees", "Temperature_Fees": "Temperature Fees", "Stethoscope_Fees": "Stethoscope Fees", "Spiro_Fees": "<PERSON><PERSON><PERSON>", "Spo2_Fees": "Spo2 Fees", "ECG_Fees": "ECG Fees", "BloodPressure_Fees": "BloodPressure Fees", "Total_Diagnostics_Fees": "Total Diagnostics Fees", "Prescription_Fees": "Prescription Fees", "Center_Wise_Investigation": "Center Wise Investigation", "Top_Centers": "Top Centers", "TOP_10": "TOP 10", "Center": "Center", "Form": "Form", "No_of_Prescription": "No.of Prescription", "Top_Medicines": "Top Medicines", "In_Queue": "In Queue", "Doctor_is_waiting": "Doctor is waiting", "Cancelled_by_doctor": "Cancelled by doctor", "Cancelled_by_you": "Cancelled by you", "Patient_screening_in_progress": "Patient screening in progress", "Patient_is_waiting": "Patient is waiting", "Cancelled_by_nurse": "Cancelled by nurse", "Edit_by_nurse": "Edit by nurse", "Doctor_Wise_Reports": "Doctor <PERSON> Reports", "Doctor_Wise_Consultation_Report": "Doctor Wise Consultation Report", "General_Consultation": "General Consultation", "Specialized_Consultation": "Specialized Consultation", "Incomplete": "Incomplete", "Patient_Wise_Details": "Patient Wise Details", "ACR(CRE)": "ACR(CRE)", "ACR(ALB)": "ACR(ALB)", "ACR(Acr)": "ACR(Acr)", "HbA1c(%)": "HbA1c(%)", "HbA1c(eAG)": "HbA1c(eAG)", "HbA1c(mmol)": "HbA1c(mmol)", "Spiro(TLC)": "Spiro (TLC)", "Spiro(TV)": "<PERSON><PERSON><PERSON> (TV)", "Spiro(PEF)": "Spiro (PEF)", "Spiro(FVC)": "Spiro (FVC)", "Spiro(FEV1)": "Spiro (FEV1)", "Spiro(FEV1%)": "Spiro (FEV1%)", "Spiro(FEF25)": "<PERSON><PERSON><PERSON> (FEF25)", "Spiro(FEF50)": "S<PERSON>ro (FEF50)", "Spiro(FEF75)": "<PERSON><PERSON><PERSON> (FEF75)", "Urine(GLU)": "<PERSON><PERSON> (GLU)", "Urine(LEU)": "<PERSON><PERSON> (LEU)", "Urine(NIT)": "Urine (NIT)", "Urine(URO)": "<PERSON><PERSON> (URO)", "Urine(PRO)": "<PERSON><PERSON> (PRO)", "Urine(pH)": "Urine (pH)", "Urine(BLO)": "<PERSON><PERSON> (BLO)", "Urine(SG)": "<PERSON><PERSON> (SG)", "Urine(KET)": "<PERSON><PERSON> (KET)", "Urine(BIL)": "<PERSON><PERSON> (BIL)", "Lipid(TC)": "L�pid (TC)", "Lipid(TG)": "L�pid (TG)", "Lipid(HDL)": "L�pid (HDL)", "Lipid(LDL)": "L�pid (LDL)", "BP(Pulse)": "BP (Pulse)", "BP(Dia)": "BP (Dia)", "BP(Sys)": "BP (Sys)", "Sys": "Sys", "Dia": "<PERSON>a", "Fetal_Count": "Fetal Count", "Stetho_Count": "Stetho Count", "Ecg_Count": "Ecg <PERSON>", "Problem_for_which_Examined": "Problem for which Examined", "Chief_Complaints": "Chief <PERSON><PERSON><PERSON><PERSON>", "Patient_Status": "Patient Status", "Project_Id": "Project Id", "Breast_Cancer_Report_Taken": "Breast Cancer Report Taken", "Foot_Analysis_Report_Taken": "Foot Analysis Report Taken", "Nurse_Wise_Reports": "Nurse Wise Reports", "Nurse_Wise_Consultation_Report": "Nurse Wise Consultation Report", "Location_Wise_Consultation_Report": "Location Wise Consultation Report", "Location_Wise_Reports": "Location Wise Reports", "Center_Wise_Report": "Center Wise Report", "Nurse_Wise_Medicine": "Nurse Wise Medicine", "Center_Wise_Medicine": "Center Wise Medicine", "onsumable": "Consumable", "Consumables_Store": "Consumables Store", "Select_quantity_provide_email_and_place_order": "Select quantity, provide email and place order", "Opps_Consumables_are_not_available": {"": "Opps Consumables are not available."}, "Enter_your_email_id_and_place_order": "Enter your email id and place order", "Place_Order": "Place Order", "XLS": "XLS", "PDF": "PDF", "CSV": "CSV", "0-20_Years": "0-20_Years", "21-40_Years": "21-40_Years", "41-60_Years": "41-60_Years", "61-100_Years": "61-100_Years", "Doctor_Wise_Diagnosis": "Doctor Wise Diagnosis", "Nurse_Wise_Diagnosis": "Nurse Wise Diagnosis", "Enter_Quantity": "Enter Quantity", "Email_Address": "Email Address", "Center_Wise_Counselling": "Center Wise Counselling", "Doctor_Wise_Counselling": "Doctor Wise Counselling", "Nurse_Wise_Counselling": "Nurse Wise Counselling", "Counselling": "Counselling", "Doctor_Wise_Counselling_Dashboard": "Doctor <PERSON> Counselling Dashboard", "Nurse_Wise_Counselling_Dashboard": "Nurse Wise Counselling Dashboard", "ALL": "ALL", "Counselling_Dashboard": "Counselling Dashboard", "Top_Counselling": "Top Counselling", "Counselling_Name": "Counselling Name", "Center_Name": "Center Name", "Top_Doctors": "Top Doctors", "Top_Nurses": "Top Nurses", "Consultation_Feedback_Summary": "Consultation Feedback Summary", "Consultation_Feedback_Report": "Consultation Feedback Report", "Date_of_1st_Examination": "Date of 1st Examination", "Date_of_2nd_Examination": "Date of 2nd Examination", "Date_of_3rd_Examination": "Date of 3rd Examination", "Date_of_4th_Examination": "Date of 4th Examination", "Date_of_5th_Examination": "Date of 5th Examination", "Medical_Test_1_done_at_Center": "Medical Test-1 done at Center", "Medical_Test_2_done_at_Center": "Medical Test-2 done at Center", "Medical_Test_3_done_at_Center": "Medical Test-3 done at Center", "Medical_Test_4_done_at_Center": "Medical Test-4 done at Center", "Medical_Test_5_done_at_Center": "Medical Test-5 done at Center", "Medical_Test_6_done_at_Center": "Medical Test-6 done at Center", "Medical_Test_7_done_at_Center": "Medical Test-7 done at Center", "Medical_Test_8_done_at_Center": "Medical Test-8 done at Center", "para_ACR": "ACR", "para_Temperature": "Temperature", "para_BloodPressure": "BloodPressure", "para_PulseOximeter": "PulseOximeter", "para_Glucose": "Glucose", "para_Rapidtest": "Rapidtest", "para_Urinetest": "Urinetest", "para_Lipid": "Lipid", "para_SpiroMeter": "SpiroMeter", "para_HBA1C": "HBA1C", "para_F200Analyzer": "F200Analyzer", "para_ECGInterpretation": "ECGInterpretation", "Center_Wise_Diagnosis_Dashboard": "Center Wise Diagnosis Dashboard", "Diagnosis_Dashboard": "Diagnosis Dashboard", "Center_Wise_Diagnosis": "Center Wise Diagnosis", "Doctor_Wise_Diagnosis_Dashboard": "Doctor Wise Diagnosis Dashboard", "Nurse_Wise_Diagnosis_Dashboard": "Nurse Wise Diagnosis Dashboard", "CenterWise_Prescription_Dashboard": "Center Wise Prescription Dashboard", "Prescription_Dashboard": "Prescription Dashboard", "Brand_Name": "Brand Name", "Doctor_Wise_Prescription_Dashboard": "Doctor Wise Prescription Dashboard", "Doctor_Wise_Medicine": "Doctor Wise Medicine", "Center_Wise_Referral_Dashboard": "Center Wise Referral Dashboard", "Referral_Dashboard": "Referral Dashboard", "top_referrals": "top referrals", "Doctor_Wise_Referrals_Dashboard": "Doctor Wise Referrals Dashboard", "Referrals_Dashboard": "Referrals_Dashboard", "Doctor_Wise_Referrals": "Doctor <PERSON> Referrals", "Nurse_Wise_Referrals": "Nurse Wise Referrals", "Investigation_Name": "Investigation Name", "Nurse_Wise_Investigation": "Nurse Wise Investigation", "Investigation_Dashboard": "Investigation Dashboard", "Doctor_Wise_Investigation": "Doctor <PERSON> Investigation", "Doctor_Wise_Investigation_Dashboard": "Doctor <PERSON> Investigation Dashboard", "English": "English", "Hindi": "Hindi", "Spanish": "Spanish", "French": "French", "No_records_found": "No records found", "SMS_Notification": "SMS Notification", "Email_Notification": "E-mail Notification", "Please_select_the_slots_to_be_deleted": "Please select the slots to be deleted.", "Write_Comment": "Write Comment", "View_Comments": "View Comments", "Register_No": "Reg No", "Doctor_Wise_Details": "Doctor <PERSON>", "Select_OP_Room": "Select OP Room", "Login_Time": "Login time", "Logout_Time": "Logout time", "Spo2%": "Spo2%", "Sys_Dia_Pulse": "Sys~Dia~Pulse", "TC_TG_HDL_LDL": "TC_TG_HDL_LDL", "HbA1c_mmol_eAG": "HbA1c(%~mmol~eAG)", "UrineTest_LEU": "UrineTest(LEU/NIT/URO/PRO/pH/BLO/SG/KET/BIL/GLU)", "Spiro_FVC": "Spiro(FVC/FEV1/FEV1%/FEF25/FEF50/FEF75/PEF/TV/TLC)", "ACR_Acr_Alb_Cre": "ACR(Acr~Alb~Cre)", "Center_Wise_Reports": "Center Wise Reports", "Center_Wise_Diagnostic_Report": "Center Wise Diagnostic Report", "Stetho": "Stetho", "Fetaldoppler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Doctor_Wise_Diagnostic_Report": "Doctor Wise Diagnostic Report", "Nurse_Wise_Diagnostic_Report": "Nurse Wise Diagnostic Report", "Glucose_Random": "Glucose Random", "Glucose_Fasting": "Glucose Fasting", "Glucose_Postprandial": "Glucose Postprandial", "LIS": "LIS", "Summary": "Summary", "Patient_Survey": "Patient Survey", "Request_for_Consumables": "Request for Consumables", "Feedback": "<PERSON><PERSON><PERSON>", "Sign_Out": "Sign Out", "Reg_No": "Reg No", "ReMeDi": "ReMeDi", "Doctor_Wise_Report": "Doctor Wise Report", "Nurse_Wise_Report": "Nurse Wise Report", "Diagnostic": "Diagnostic", "ACR": "ACR", "Referrals_Name": "Referrals Name", "How_did_you_come_to_know_about_the_Center?": "How did you come to know about the Center?", "For_which_health_problem_did_you_go_to_the_Center?": "For which health problem did you go to the Center?", "Were_any_pathological_test_done_at_the_Center?": "Were any pathological test done at the Center?", "If_yes_then_which_all_tests_were_done?": "If yes, then which all tests were done?", "Did_doctors_from_Nanavati_Hospital_discuss_your_health_condition_over_the_web_camera?": "Did doctors from Nanavati Hospital discuss your health condition over the web camera?", "Did_the_Center_give_you_any_prescription_for_medicine?": "Did the Center give you any prescription for medicine?", "As_per_the_doctor_prescription_Did_you_take_medicine?": "As per the doctor prescription, Did you take medicine?", "Did_you_complete_course_of_medicine_as_prescribed_by_doctor?": "Did you complete course of medicine as prescribed by doctor?", "Did_you_feel_better_by_this_treatment?": "Did you feel better by this treatment?", "Did_you_go_to_the_Center_again?": "Did you go to the Center again?", "If_not_then_why?": "If not, then why?", "Would_you_prefer_being_diagnosed": "Would you prefer being diagnosed?", "How_much_would_you_pay_if_this_all_tests_and_treatment_were_done_by_another_doctor?": "How much would you pay if this all tests and treatment were done by another doctor?", "Did_any_other_person_from_your_family_go_to_the_Center_for_treatment?": "Did any other person from your family go to the Center for treatment?", "Did_you_refer_any_other_person_to_the_center_for_treatment?": "Did you refer any other person to the center for treatment?", "What_other_facility_do_you_want_at_the_Center?": "What other facility do you want at the Center?", "How_is_the_behavior_of_the_staff_working_at_the_TMC_Center?": "How is the behavior of the staff working at the TMC Center?", "How_did_you_find_cleanliness_and_other_facilities_like_water_toilets_etc_at_the_center?": "How did you find cleanliness and other facilities like water, toilets etc at the center?", "Consultation_Fee_Off": "off", "Range": "Range", "Survey": "Survey", "Survey_Details": "Survey Details", "Waist": "Waist", "Hip": "Hip", "If_anyone_in_your_family_(blood_relation*)_have/had_history_of_diabetes_or_hypertension_then_Enter": "If anyone in your family (blood relation*) have/had history of diabetes or hypertension then Enter", "Medication_name": "Medication name", "How_many_days_did_you_take_it?": "How many days did you take it?", "How_many_times_per_day_did_you_take_it?": "How many times per day did you take it?", "How_much_did_you_take_each_time?": "How much did you take each time?", "How_many_times_did_you_miss_taking_it?": "How many times did you miss taking it?", "For_what_reason_were_you_taking_it?": "For what reason were you taking it?", "How_well_does_this_medicine_work_for_you?": "How well does this medicine work for you?", "Medication_Name_bother_you_in_any_way": "Medication Name bother you in any way", "In_what_way_does_it_bother_you?": "In what way does it bother you?", "My_medication_causes_side_effects": {"": "My medication causes side effects."}, "It_is_hard_to_remember_all_the_doses": "It is hard to remember all the doses", "It_is_hard_to_pay_for_the_medication": "It is hard to pay for the medication", "It_is_hard_to_open_the_container": {"": "It is hard to open the container."}, "It_is_hard_to_get_my_refill_on_time": "It is hard to get my refill on time", "It_is_hard_to_read_the_print_on_the_container": "It is hard to read the print on the container", "The_dosage_times_are_inconvenient": "The dosage times are inconvenient", "My_medication_causes_other_problem_or_concern": "My medication causes other problem or concern", "If_other_problem_or_concern_please_explain": "If other problem or concern, please explain", "If_you_stop_taking_any_medications_in_the_PAST_SIX_MONTHS_then_Medication_name": "If you stop taking any medications in the PAST SIX MONTHS then, Medication name", "How_well_did_the_medicine_work_for_you?": "How well did the medicine work for you?", "How_much_did_it_bother_you?": "How much did it bother you?", "For_what_reason_did_you_stop_taking_it?": "For what reason did you stop taking it?", "No_of_days_consuming_fruits": "No of days consuming fruits", "No_of_servings_you_eat_on_typical_day": "No of servings you eat on typical day", "No_of_days_consuming_Vegetable": "No of days consuming Vegetable", "Type_of_Physical_activity": "Type of Physical activity", "Self-testing_of_the_blood_glucose": "Self-testing of the blood glucose", "Self-testing_of_the_blood_pressure": "Self-testing of the blood pressure", "Foot_care": "Foot care", "Alcohol_use_Current_user": "Alcohol use Current user", "Frequency_of_alcohol_use": "Frequency of alcohol use", "Tobacco_use-smoke_Current_user": "Tobacco use-smoke Current user", "Frequency_of_Tobacco_use": "Frequency of Tobacco use", "Fear_of_side_effects_of_medicines?": "Fear of side effects of medicines?", "Distressed_with_differential_food_in_the_family?": "Distressed with differential food in the family?", "Fear_of_complications_Of_NCD?": "Fear of complications Of NCD?", "Difficult_to_keep_up_with_the_routine?": "Difficult to keep up with the routine?", "Inability_of_the_family_members": "Inability of the family members", "Distressed_with_social_issues": "Distressed with social issues", "Nurse_Wise_Prescription_Dashboard": "Nurse Wise Prescription Dashboard", "HBA1C": "HbA1c", "Center_Wise_Prescription_Dashboard": "Center Wise Prescription Dashboard", "top_medicines": "top medicines", "Press_No_if_youwant_to_continue_work": {"_Press_Yes_to_logout_current_user": {"": "Press No if you want to continue work. Press Yes to logout current user."}}, "Center_Wise_Referrals": "Center Wise Referrals", "HH_Survey_Report": "HH Survey Report", "as_per_Friedewald_Method": "as per \"Friedewald\" Method", "as_per_Iranian_Method": "as per \"Iranian\" Method", "Patient_Contact_Details": "Patient Contact Details", "Pharmacy_Details": "Pharmacy Details", "Fetal_doppler_pulse": "Fetal doppler pulse", "�_ALB": "�-ALB", "Foot_analysis": "Foot analysis", "Breast_cancer": "Breast cancer", "Complete_Consultation": "Complete Consultation", "Incomplete_Consultation": "Incomplete Consultation", "New_Consultation": "New Consultation", "FollowUp_Consultation": "FollowUp Consultation", "General_Consultation_Remote": "General Consultation (Remote)", "Specialized_Consultation_Remote": "Specialized Consultation (Remote)", "Pulse_oximeter": "Pulse-oximeter", "Glucometer": "Glucometer", "Spiro": "<PERSON><PERSON><PERSON>", "Rapid_Test": "Rapid Test", "L_sec": "L/sec", "from": "from", "Result_is": "Result is", "pH": "pH", "KETONE": "KETONE", "U_ALB": "&micro;ALB", "FootAnalysisReport": "Foot Analysis Report", "BreastCancerScreening": "Breast Cancer Screening", "New_Consent_Request": "New Consent Request", "Consent_Request_Form": "Consent Request Form", "Patient_Identifier": "Patient Identifier", "Purpose_Of_Request": "Purpose of request", "Health_Info_From": "Health info from", "Health_Info_To": "Health info to", "Health_Info_Type": "Health info type", "Consent_Expiry": "Consent Expiry", "Submit_Consent_Form": "Submit Consent Form", "CAREMGT": "Care Management", "BTG": "Break the Glass", "PUBHLTH": "Public Health", "HPAYMT": "Healthcare Payment", "DSRCH": "Disease Specific Healthcare Research", "PATRQT": "Self Requested", "DiagnosticReport": "Diagnostic Report", "OPConsultation": "OP Consultation", "DischargeSummary": "Discharge Summary", "ImmunizationRecord": "Immunization Record", "HealthDocumentRecord": "Record artifact", "WellnessRecord": "Wellness Record", "Add_Diagnosis": "Add Diagnosis", "Reset_Button": "Reset", "Please_Enter_Diagnosis": "Please Enter Diagnosis", "Please_Select_Sub_chapter": "Please Select Sub Chapter", "Please_Select_Category": "Please Select Category", "Enter_Diagnosis": "Enter Diagnosis", "Enter_ICD_Code": "Enter ICD Code", "Select_Sub_Chapter": "Select Sub Chapter", "Sunday": "Sunday", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "masculino": "Male", "hembra": "Female", "otro": "Other", "Fetal_Doppler_Data": "Fetal Doppler Data", "Recommondations": "Recommondations", "Intervention": "Intervention", "from_time": "From Time", "till_time": "To Time", "Summary_Parameter": "Summary", "Summary_Medicines": "Summary", "Summary_Diagnosis": "Summary", "Summary_Investigation": "Summary", "Summary_Counselling": "Summary", "cms": "cms", "Kgs": "Kgs", "Lip_TC": "Lip TC", "Minutes": "Minutes", "Please_provide_contact_number": "Please provide contact number.", "Add_New_Instruction": "Add New Record", "Add_New_Referral": "Add New Record", "Add_New_Investigation": "Add New Record", "Add_New_Medication": "Add New Record", "Add_New_Complaint": "Add New Record", "Add_Instruction": "Add New Record", "Add_Referral": "Add New Record", "Add_Investigation": "Add New Record", "Add_Medication": "Add New Record", "Add_Complaint": "Add New Record", "Medication": "Medicines", "take_live_photo": "Capture Image", "Existing_Patient": "Existing Patient", "from_date": "From Date", "till_date": "To Date", "Session": "Session", "StartTime": "Start Time", "N/A": "N/A", "hour": "hour", "minute": "minute", "second": "second", "Contact_person_mobile_number": "Contact person mobile number.", "Please_provide_patient_details": "Please provide patient details.", "Year": "Year", "Month": "Month", "Day": "Day", "and": "and", "BloodPressure": "BloodPressure", "glucose": "Glucose", "ecg": "Electro Cardio Gram", "Top_Investigation_Doctors": "Top Doctors", "Top_Counselling_Doctors": "Top Doctors", "country": "Country", "state": "State", "district": "District", "block": "Block", "village": "Village", "city": "City", "Phone_Number_Min_Length": "Phone Number Min Length", "Phone_Number_Max_Length": "Phone Number Max Length", "Specialization": "Specialization", "Time_of_Examination": "Time of Examination", "Add_Medicine_Name": "Add Medicine Name", "error_occured_while_adding_medicine_name": "error occured while adding Medicine Name", "Generic_Name": "Generic Name", "No_of_Days_Cannot_Be_0_please_enter_valid_number": "No.of Days Cannot be 0. Please Enter Valid Number.", "Medicine_Inventory": "Medicine Inventory", "Please_enter_No_of_days_(b/w_1_to_100)": "Please enter No. of days b/w 1 to 999.", "Please_enter_duration_(number_b/w_1_to_100)": "Please enter Duration (number b/w 1 to 100).", "CONSULTATION_BALANCE_OVER": "Insufficient consultation balance. Please contact your administrator.", "Center_Nurse_Wise_Report": "Center Nurse Wise Report", "Medicine_Stock_Details": "Medicine Stock Details", "Name_of_Drug": "Name of Drug", "Type": "Type", "Expired_Medicines": "Expired Medicines", "Total_issued_medicines_from": "Total issued medicines from", "New_Stock_Added": "New Stock Added", "As_per_old_count": "As per old count", "Available_Quantity": "Available Quantity", "Quantity_To_Dispense": "Quantity To Dispense", "Please_enter_a_number_less_than_or_equal_to": "Please enter a number less than or equal to", "dispensed_Date": "Dispensed Date", "NOT_YET_DISPENSED": "Not Yet Dispensed", "DUPLICATE_BATCH_EXPIRY_ENTRY": "Duplicate Batch Number & Expiry.", "First_name_not_should_be_null_string": "First Name Not should Be Null String", "Last_name_not_should_be_null_string": "Last Name Not should Be Null String", "Nurse_Id": "NURSE ID", "Dashboard_quicksight": "Dashboard", "fasting_blood_sugar": "Fasting Blood Sugar", "Postprandial_Blood_Sugar": "Post-prandial Blood Sugar", "Random_Blood_Sugar": "Random Blood Sugar", "Date_Of_Birth": "DOB", "Mobile_Number": "Mobile", "Unit_Modify": "Unit Configuration", "Height_Unit": "Height Unit", "Weight_Unit": "Weight Unit", "User_already_exist": "User already exist", "health_number": "Health number", "health_address": "Health Address", "abha_address": "ABHA Address", "abha_number": "ABHA Number", "ABHA_Address": "ABHA Address", "Health_number_address": "ABHA NUMBER/ABHA ADDRESS", "Specialized_New_Consultation": "Specialized Completed New Consultation", "Specialized_FollowUp_Consultation": "Specialized Completed FollowUp Consultation", "General_New_Consultation": "General Completed New Consultation", "General_FollowUp_Consultation": "General Completed FollowUp Consultation", "Total_Complete_Consultation": "Total Completed Consultation", "Specialized_Complete_Consultation": "Specialized Completed Consultation", "General_Complete_Consultation": "General Completed Consultation", "Choose_Days": "Choose <PERSON>", "Follow_Up_days": "Follow-up days", "Please_Enter_folloeUp_Days_In_Number": "Please Enter follow up days in number", "MARK_AS_COMPLETE": "Mark as Complete", "MARK_AS_ACTIVE": "<PERSON> as Active", "OTP_SENT": "Enter the OTP sent on", "VALIDATE_OTP": "Validate OTP", "LOGIN_CREDENTIALS_SENT_ON_REGISTERED_MAIL_ID": "User login credentials sent on $", "PLEASE_PROVIDE_MAIL_ID": "Please provide mail Id", "PLEASE_PROVIDE_UID": "Please provide UID", "YOU_ENTERED_INVALID_OTP": "You entered invalid OTP", "SEND_OTP_ON_MAIL_TEMPLATE": "Dear $name, <br><br>OTP to register with ReMeDi digital healthcare system is $OTP. Please do not share this OTP with anyone.<br><br>Regards,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_APPOINTMENT_BOOKED_TEMPLATE": "Dear $patient_name, <br><br> Your appointment with $Dr_name has been confirmed on $DATE at $TIME. Please ensure that you are on time for the appointment. Thanks!<br><br><PERSON><PERSON>,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_APPOINTMENT_CANCELLED_TEMPLATE": "Dear $patient_name, <br><br> Your appointment with $Dr_name on $DATE at $TIME has been cancelled. Thanks! <br><br>Regards,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_APPOINTMENT_RESCHEDULED_TEMPLATE": "Dear $patient_name, <br><br> Your appointment with $Dr_name  on $DATE1 at $TIME1 has been rescheduled to $DATE2 at $TIME2. Please ensure that you are on time for the appointment. Thanks!<br><br><PERSON><PERSON>,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_CONSULTATION_COMPLETED_TEMPLATE": "Dear $patient_name, <br><br>You have completed your consultation with $Dr_name on $DATE at $TIME. Wish you a speedy recovery! Thanks!<br><br>Regards,<br>ReMeDi Digital Healthcare Team", "SEND_USERNAME_PASSWORD_ON_EMAIL_AFTER_REGISTRATION_TEMPLATE": "Dear $patient_name, <br><br>Thank you for registering on the ReMeDi Digital Health Platform! You can access your account using the following credentials.<br><br><PERSON>rname", "SEND_EMAIL_NOTIFICATE_ON_APPOINTMENT_SUSPENDED_TEMPLATE": "Dear $patient_name,<br><br> Your appointment with $Dr has been Suspended at $TIME on $DATE. Thanks!<br><br>Regards,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_CONSULTATION_COMPLETED_TEMPLATE_DOCTOR": "Dear $Dr, You have completed your appointment on $DATE at $TIME. Thanks! <br><br><PERSON><PERSON>,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_APPOINTMENT_RESCHEDULED_TEMPLATE_DOCTOR": "Dear $Dr, The appointment scheduled on $DATE1 at $TIME1 with you has been rescheduled on $DATE2 at $TIME2. Thanks!<br><br><PERSON>ards,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_APPOINTMENT_CANCELLED_TEMPLATE_DOCTOR": "Dear $Dr, The appointment scheduled on $DATE at $TIME with you has been cancelled. Thanks!<br><br><PERSON><PERSON>,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_APPOINTMENT_BOOKED_TEMPLATE_DOCTOR": "Dear $Dr, An appointment has been confirmed with you on $DATE at $TIME. Please be available on time for the appointment. Thanks!<br><br><PERSON><PERSON>,<br>ReMeDi Digital Healthcare Team", "SEND_EMAIL_NOTIFICATE_ON_APPOINTMENT_SUSPENDED_TEMPLATE_DOCTOR": "Dear $Dr, Your appointment with $patient_name  has been Suspended at $TIME on $DATE. Thanks!<br><br>Regards,<br>ReMeDi Digital Healthcare Team", "FollowUp_Complete_Consultation": "Follow-up Completed", "SEND_OTP_FORGOT_PASSWORD_ON_MAIL_TEMPLATE": "Dear $name, <br><br> Your OTP for password reset on ReMeDi digital healthcare system is $OTP. Please do not share this OTP with anyone.<br><br>Regards,<br>ReMeDi Digital Healthcare Team", "INVALID_EMAIL_ID": "Invalid Email ID", "OTP_Mail_has_been_successfully": "OTP has been sent successfully on mail.", "OTP_Sending_Mail_Failed": "Failed to send OTP on mail.", "YOU_HAVE_AN_APPOINTMENT_WITH_SAME_SLOT_WITH_ANOTHER_DOCTOR": "You have an appointment with same slot with another doctor. Please select different slot.", "INVALID_HEIGHT": "Invalid Height", "PLEASE_SELECT_PATIENT_STATUS": "Please select patient status", "second_Configuration": "Consultation Timer Configuration", "please_enter_seconds(b/w_0_to_999)": "Please enter seconds b/w 0 to 999.", "Diabetes": "Diabetes", "Hypertension": "Hypertension", "Both": "Both", "Normal": "Normal", "Select_Status": "Select status", "timeOffset": "TimeOffset(UTC)", "Consultation_Detail": "Consultation Detail", "patient_wise_report": "Patient Wise Report", "Patient_Wise_Consultation_Report": "Patient Wise Consultation Report", "Total_Slot": "Total Slot", "Available_Slot": "Available Slot", "Used_Slot": "Used Slot", "Slot_Detail": "Slot Detail", "Doctor_Wise_Slot_Details": "Doctor <PERSON> Slot Details", "Patient_Name_MIS": "Patient Name", "Age_MIS": "Age", "Mobile_MIS": "Contact Number", "DOB_MIS": "Date of Birth", "Cancelled_by_patient": "Cancelled By patient", "Duration_filter": "Duration Filter", "DENGUE_IGG_IGM": "Dengue IgG/IgM", "Password_rule": "Password must be minimum of 8 characters,should contain at-least 1 Uppercase,1 Lowercase,1 Numeric and 1 Special Character", "File_size_should_be_less_than_5MB": "File size should be less than 5MB.", "File_name_is_required": "File name is required.", "Invalid_file_extension": "Invalid file extension. Only JPG, JPEG, PNG, BMP, GIF, TIF, ZIP, PDF files are allowed.", "consent1": "You can create your ABHA number using <PERSON><PERSON><PERSON><PERSON> instantaneously. Please ensure that <PERSON><PERSON><PERSON><PERSON> is linked to a Mobile Number as an OTP authentication will follow. If you do not have a Mobile Number linked, visit the nearest ABDM participating facility and seek assistance.", "consent2": "I am voluntarily sharing my Aadhaar Number / Virtual ID issued by the Unique Identification Authority of India (\\u201CUIDAI\\u201D), and my demographic information for the purpose of creating an Ayushman Bharat Health Account number (\\u201CABHA number\\u201D) and Ayushman Bharat Health Account address (\\u201CABHA Address\\u201D). I authorize NHA to use my Aadhaar number / Virtual ID for performing Aadhaar based authentication with UIDAI as per the provisions of the Aadhaar (Targeted Delivery of Financial and other Subsidies, Benefits and Services) Act, 2016 for the aforesaid purpose. I understand that UIDAI will share my e-KYC details, or response of \\u201CYes\\u201D with NHA upon successful authentication.", "consent3": "I consent to usage of my ABHA address and ABHA number for linking of my legacy (past) health records and those which will be generated during this encounter.", "consent4": "I authorize the sharing of all my health records with healthcare provider(s) for the purpose of providing healthcare services to me during this encounter.", "consent5": "I consent to the anonymization and subsequent use of my health records for public health purposes.", "consent6": "I, #_<PERSON><PERSON><PERSON>, confirm that I have duly informed and explained the beneficiary of the contents of consent for aforementioned purposes.", "consent7": "I, have been explained about the consent as stated above and hereby provide my consent for the aforementioned purposes.", "Create_ABHA_Number": "Create ABHA Number", "Using_Aadhaar": "Using <PERSON><PERSON>", "Enter_Aadhaar_Number": "En<PERSON>", "Generate_OTP": "Generate OTP", "ABHA_Verification": "ABHA Verification (For existing ABHA user)", "Enter_ABHA_number": "Enter ABHA number/Address", "OTP_has_been_sent": "OTP has been sent to your linked number.", "Aadhaar": "<PERSON><PERSON><PERSON><PERSON>", "Verify_OTP": "Verify OTP", "Your_Profile_Details": "Your Profile Details", "Validate_using": "Validate using", "As_per_Aadhaar": "(As per <PERSON><PERSON><PERSON><PERSON>)", "Full_name": "Full name", "Please_enter_your_mobile_number": "Please  enter your mobile number, preferably the one linked with <PERSON><PERSON><PERSON><PERSON>.", "Download_ABHA_card": "Download ABHA card", "Authenticate_your_ABHA_number": "This mobile number will be used to authenticate your ABHA number", "Link_ABHA_Address": "Link ABHA Address", "Suggestions": "Suggestions", "Congratulations": "Congratulations!! Linking has been done successfully ,", "InvalidCaptcha": "<PERSON><PERSON><PERSON>", "Please_enter_captcha": "Please Enter Captcha", "Consent8": "I hereby declare that", "Enter_valid_number": "Please enter valid number", "File_size_should_be_less_than_2MB": "File size should be less than 2MB.", "ABHA_PATIENT_REGISTRATION_QUEUE": "Patient <PERSON>", "ISABHAREG": "ABHA Patient Registration", "CREATE_PATIENT_ABHA": "Create New Record", "ABDM_RECORD": "ABDM Record", "DESK_CONFIGURATION": "Desk Configuration", "DESK_NAME": "Desk Name", "DESK_NUMBER": "Desk Number", "DESK_DESCRIPTION": "Desk Description", "GROUPING": "Grouping", "SELECT_DESK": "Select Desk", "PLEASE_SELECT_DESK": "Please Select Desk", "PLEASE_ENTER_UNIQUE_ID": "Enter Unique Id", "YOU_ALREADY_HAVE_APPOINTMENT_ON_SAME_DAY": "This patient already has an appointment for the same day.", "DESK_STATUS": "Desk Status", "SEND_TO_DOCTOR": "Send To Doctor", "SEND_TO_DESK": "Send To Desk", "CANNOT_START_IN_CONSULTATION_APPOINTMENT": "Patient is in consultation with <PERSON>. You can not start this consultation.", "YOU_HAVE_ACTIVE_SUBSCRIPTION": "You have active subscription plan!", "SUBSCRIPTION_PLAN_DETAILS": "Subscription Plan Details", "PLAN_NAME": "Plan Name", "REMAINING_CONSULTATIONS": "Remaining Free Consultation", "ALLOCATED_CONSULTATION": "Allocated Free Consultation", "PLAN_EXPIRY_DATE": "Plan expiry date", "YOU_DO_NOT_HAVE_ACTIVE_PLAN": "You do not have any active subscription plan!", "YOU_CAN_NOT_START_SENT_CONSULTATIONS": "Consultation is sent to the Doctor. You can not start this consultation.", "YOUR_SUBSCRIPTION_PLAN_IS_EXPIRED": "Your current subscription plan has expired. Please renew your subscription to continue using the service.", "CHARGES_MAY_APPLY": "If you choose to continue without renewal, charges may apply.", "REFERRALS_MASTER_LIST_CONFIGURATION": "Referrals Master List Configuration", "HOSPITAL_NAME": "Hospital Name", "HOSPITAL_CODE": "Hospital Code", "HOSPITAL_ADDRESS": "Hospital Address", "HOSPITAL_MAIL_ID": "Hospital Email Id", "HOSPITAL_CONTACT_NUMBER": "Hospital Contact Number", "SEND_EMAIL_REFFERAL_TEMPLATE": "Dear $to_dr_name,<br/><br/>This email serves as a referral for $patient_name, a patient of $referring_dr_name at Jubicare Smart Clinics.<br/> $patient_name with Unique ID- $unique_code is presenting with Remark-$remark. We have attached $referring_dr_name's consultation notes for your reference. <br/> We believe that your expertise would be of great benefit to $patient_name.<br/><br/> Our clinic contact information is also provided below for any questions you may have regarding the patient's medical history. <br/><br/> <b>Jubicare Smart Clinics</b><br/> Phone number- **********<br/> Email address- <EMAIL><br/>Thank you for your time and consideration. We look forward to collaborating with you to provide optimal care. <br/><br/>Sincerely,<br/>$referring_dr_name<br/>Jubicare Smart Clinics", "SEND_EMAIL_REFFERAL_SUBJECT": "Referral for Patient $patient_name from Jubicare Smart Clinics", "REMARKS_FIELD_NOT_EMPTY": "Remarks field must not be empty", "REMARKS": "Remarks", "DOCTOR_EMAIL_ID": "Doctor <PERSON><PERSON>", "DOCTOR_CONTACT_NUMBER": "Doctor Contact Number", "FAMILY_CARD_ID": "Family Card Id", "HEAD_OF_HOUSEHOLD": "Head Of Household", "UNIQUE_ID": "Unique Id", "SUBCRIPTION_PLAN_NAME": "Subscription Plan Name", "SUBSCRIPTION_PLAN_ISSUED_DATE": "Subscription Plan Issued Date", "SUBSCRIPTION_PLAN_EXPIRY_DATE": "Subscription Plan Expiry Date", "ALLOCATED_FREE_CONSULTATIONS": "Allocated Free Consultations", "USED_FREE_CONSULTATIONS": "Used Free Consultations", "REMAINING_FREE_CONSULTATIONS": "Remaining Free Consultations", "NAME_OF_FAMILY_MEMBER": "Name Of Family Member", "CONSULTATION_DATE": "Consultation Date", "FAMILY_MEMBER_WISE_REPORT": "Family Member Wise Report", "POWERED_BY": "Powered by", "CONSULTATION_ID": "Consultation ID", "CONSULTATION_FEE": "Consultation Fee", "FREE": "FREE", "UNIQUE_CODE": "Unique Code", "QUEUE_NUMBER": "Queue Number", "CONSULTATION_INVOICE": "Consultation Invoice", "OTP_send_to_linked_mobile_number": "OTP sent to the linked Mobile number", "ADD_MEDICINE": "Add Medicine", "NO_OF_DAYS_MEDICATION": "No Of Days", "INVALID_ABHA_NUMBER": "Invalid Health Number", "INVALID_ABHA_ADDRESS": "Invalid Health Address", "PROVIDE_PATIENT_ID_UNIQUE_ID": "Please provide patient name or Unique id", "CLEAR": "Clear", "client-id-client-secret-config": "FHIR Client-Id & Client-Secret Config", "client-id-client-secret-generate-btn": "Generate Client-Id & Client-Secret", "Privacy_Policy": "Privacy Policy", "SELECT_VERIFICATION_METHOD": "Select the Verification Method", "SELECT_VALIDATION_METHOD": "Select the Validate", "PROCEED": "Proceed", "ENTER": "Enter the", "OTP_send_to_aadhaar_linked_mobile_number": "OTP sent to Aadhaar linked mobile number", "INVALID_MOBILE_NUMBER": "Invalid Mobile Number", "Glucose_BLE": "Glucose (Wireless)", "Please_Switch_On_Glucose_BLE_Device": "Please Switch On Glucose (Wireless) Device", "BLOOD_GROUPING": "Blood Grouping", "A_POSITIVE_(A+)": "A Positive (A+)", "A_NEGATIVE_(A-)": "A Negative (A-)", "B_POSITIVE_(B+)": "B Positive (B+)", "B_NEGATIVE_(B-)": "B Negative (B-)", "AB_POSITIVE_(AB+)": "AB Positive (AB+)", "AB_NEGATIVE_(AB-)": "AB Negative (AB-)", "O_POSITIVE_(O+)": "O Positive (O+)", "O_NEGATIVE_(O-)": "O Negative (O-)", "GeneralExaminationCamera": "General Examination Camera", "General_Examination_Camera": "GeneralExaminationCamera", "param_GeneralExaminationCamera": "GeneralExaminationCamera", "PLEASE_SELECT_DEVICE": "Please Select Device", "GlUCONAVII_PRO": "GlucoNavii PRO", "LIPIDOCARE_WIRELESS": "LipidoCare (Wireless)", "LIPIDOCARE_CABLE": "LipidoCare (Cable)", "LIPIDOCARE_WIRELESS_INSTRUCTION_I": "Ensure that your device is nearby.", "LIPIDOCARE_WIRELESS_INSTRUCTION_II": "Turn on the device.", "LIPIDOCARE_WIRELESS_INSTRUCTION_III": "Press and hold the power button to activate Bluetooth pairing mode.", "LIPIDOCARE_WIRELESS_INSTRUCTION_IV": "Switch off the device after receiving the 'OK' message on the display.", "LIPIDOCARE_WIRELESS_INSTRUCTION_V": "Press and hold the power button while the device is turned off to retrieve the reading.", "LIPIDOCARE_DEVICE_UNREACHABLE": "LipidoCare device unreachable. Ensure it's nearby and retake the reading.", "IS_LIPIDOCARE_DEVICE_PAIRED": "Have any readings been previously recorded with this device?", "CONFIRM_ACTION": "Confirm Action", "Audit_Trails": "Audit Trails", "Submit": "Submit", "Activity_Log_Report": "Activity Log Report", "SR_NO": "SR.NO.", "User_Id": "User Id", "Login_User": "Login User", "Operation": "Operation", "access_Time": "Access Time", "Domain_ID": "Domain ID", "Remote_IP": "Remote IP", "Book_Appointment": "Book Appointment", "PLEASE_CONFIGURE_PHONE_NO_LENGTH": "Please Configure Phone Number Length.", "SICKLE_CELL": "Sickle Cell", "Other_Hemoglobinopathies_or_Thalassemia_or_Other_Hemoglobinopathies_Associated_With_Thalassemia": "Other Hemoglobinopathies or Thalassemia or Other Hemoglobinopathies Associated With Thalassemia", "Sickle_Cell_Disease_(SCD)_/_Sickle_Cell_With_Other_Hemoglobinopathie": "Sickle Cell Disease (SCD) / Sickle Cell With Other Hemoglobinopathie", "Sickle_Cell_Trait_or_Its_Association_With_Other_Hemoglobinopathies": "Sickle Cell Trait or Its Association With Other Hemoglobinopathies", "Result_is_Other_Hemoglobinopathies_or_Thalassemia_or_Other_Hemoglobinopathies_Associated_With_Thalassemia": "Other Hemoglobinopathies or Thalassemia or Other Hemoglobinopathies Associated With Thalassemia", "Result_is_Normal": "Normal", "Result_is_Sickle_Cell_Disease_(SCD)_/_Sickle_Cell_With_Other_Hemoglobinopathie": "Sickle Cell Disease (SCD) / Sickle Cell With Other Hemoglobinopathie", "Result_is_Sickle_Cell_Trait_or_Its_Association_With_Other_Hemoglobinopathies": "Sickle Cell Trait or Its Association With Other Hemoglobinopathies", "Unit_Are_Updated": "Unit are updated", "Please_Select_Matching_Unit": "Please select matching Unit", "Problem_In_Updating_Units": "problem in updating units", "Seconds_Are_Updated": "Seconds are updated", "Problem_In_Updating_Seconds": "problem in updating seconds", "Days_Are_Updated": "days are updated", "Problem_In_Updating_Days": "problem in updating days", "Please_Enter_Seconds": "Please enter seconds", "Cms": "Cms", "Feet": "Feet", "Kg": "Kg", "lbs": "lbs", "TestName": "TestName", "Upload_File_Size_Must_be_less_than_5_Mb": "Upload File Size Must be less than 5 Mb", "The_uploaded_PDF_may_contain_JavaScript_code": "The uploaded PDF may contain JavaScript code.", "ft": "ft", "in": "in", "inch": "inch", "TimeZone_Filter": "TimeZone Filter", "Select_Time_Zone": "SelectTime Zone", "Invalid_file_type_Please_select_an_image_(jpg,jpeg,or,png)": "Invalid file type. Please select an image (jpg, jpeg, or png)", "Doctor_has_been_Disabled_for": "Doctor has been disabled for", "Doctor_has_been_Enabled_for": "Doctor has been enabled for", "Disabled_successfully": "Disabled Successfully", "Enabled_successfully": "Enabled Successfully", "SNOMED_CT_Code": "SNOMED CT CODE", "Consent_Request_Initiated": "Consent Request Initiated", "Enter_Seconds": "Enter Seconds", "Parameter_has_been": "Parameter has been", "Domain_created_successfully": "Domain created successfully!", "Domain_has_been": "Domain has been", "Domain_Pharmacy_has_been": "Domain Pharmacy Has Been", "Enter_SNOMED_CT_Code": "Enter SNOMED CT Code", "HbA1c_BLE": "HbA1c (Wireless)", "Please_Switch_On_HbA1c_BLE_Device": "Please Switch On HbA1c (Wireless) Device", "para_HBA1CBLE": "HBA1CBLE", "HBA1CBLE_ManualEntry": "HbA1c ManualEntry", "ABDM_CARECONTEXT_LIST": "ABDM Care Context List", "REFERENCE_NO": "Reference No", "RECORD_TYPE": "Record Type", "RECORD_DATE": "Record Date", "RECORD_LINKED": "Record Linked", "ECG_Interpretation": "ECG Interpretation", "ECG_Interpretation_Fees": "ECG Interpretation Fees", "ECG_Interpretation_Count": "Ecg Interpretation Count", "param_ECGInterpretation": "ECG Interpretation", "The_ECG_has_been_sent_for_interpretation_Please_check_past_records_for_the_relevant_report": "The ECG has been sent for interpretation. Please check past records for the relevant report.", "Ophthalmoscope": "Ophthalmoscope", "Ophthalmoscope_fees": "Ophthalmoscope Fees", "Ophthalmoscope_Count": "Ophthalmoscope Count", "WBCDIFF": "White Blood Cell Differential", "para_WBCDIFF": "WBCDIFF", "param_WBCDIFF": "White Blood Cell Differential", "WBCDIFF_ManualEntry": "White Blood Cell Differential Manual Entry", "WBC": "WBC", "Total_WBC_Count": "Total WBC Count", "NEUTROPHILS": "NEUTROPHILS", "LYMPHOCYTES": "LYMPHOCYTES", "MONOCYTES": "MONOCYTES", "EOSINOPHILS": "EOSINOPHILS", "BASOPHILS": "BASOPHILS", "FAILED_TO_SAVE_WBC_DIFF_READING": "Unable to save WBC Differential Reading. Please try again.", "SUCCESSFULLY_SAVED_WBCDIFF_DATA": "Successfully saved WBC differential data.", "param_Ophthalmoscope": "Ophthalmoscope", "You_are_being_redirected_to_the_forus_webpage": "You are being redirected to the forus webpage.", "Report_will_be_available_in_the_past_records": "Report will be available in the past records", "Click_on_proceed_to_continue": "Click on proceed  to continue.", "Refracto_Meter": "Auto Refractometer", "Browse_Computer_for_Pdf": "Browse Computer for Pdf", "Download_Report": "Download Report", "PDF_uploaded_successfully": "PDF uploaded successfully.", "param_AutoRefractometer": "AutoRefractometer", "ophthalmoscope_access": "Ophthalmoscope Access", "Download_Interpreted_Report": "Download Interpreted Report", "Invalid_file_format_Only_PDF_files_are_allowed": "Invalid file format. Only PDF files are allowed.", "No_file_selected": "No file selected", "Please_select_a_PDF_file_to_upload": "Please select a PDF file to upload.", "Report_is_not_generated_yet_Please_check_after_some_time": "Report is not generated yet. Please check after some time.", "Auto_Refractometer_Taken": "Auto Refractometer Taken", "Eye_Care": "Eye Care", "Browse_Computer_for_File": "Browse Computer for File", "Flow": "Flow ( liters per second )", "Volume": "Volume (L)", "enter_your_age": "Enter Age", "PLEASE_ENTER_NATIONAL_ID": "Enter National Id", "Please_Provide_National_Id_first": "Please provide National ID.", "Please_Provide__Valid_National_Id": "Please provide a Valid National ID.", "NT": "Neuropathy", "GlucoseBLE": "GlucoseBLE", "Consultation_start_time": "Consultation Start Time", "Consultation_end_time": "Consultation End Time", "Result_is_Other_Hemoglobinopathies_or_Thalassemia": "Result is Other Hemoglobinopathies or Thalassemia or Other Hemoglobinopathies Associated With Thalassemia", "Result_is_A_POSITIVE_(A+)": "Result is A Positive (A+)", "Result_is_A_NEGATIVE_(A-)": "Result is A Negative (A-)", "Result_is_B_POSITIVE_(B+)": "Result is B Positive (B+)", "Result_is_B_NEGATIVE_(B-)": "Result is B Negative (B-)", "Result_is_AB_POSITIVE_(AB+)": "Result is AB Positive (AB+)", "Result_is_AB_NEGATIVE_(AB-)": "Result is AB Negative (AB-)", "Result_is_O_POSITIVE_(O+)": "Result is O Positive (O+)", "Result_is_O_NEGATIVE_(O-)": "Result is O Negative (O-)", "Result_is_P": {"v_&_P": {"f_Positive": "Result is P.v & P.f Positive", "f_Negative": "Result is P.v & P.f Negative"}, "v_Positive_&_P": {"f_Negative": "Result is P.v Positive & P.f Negative"}, "v_Negative_&_P": {"f_Positive": "Result is P.v Negative & P.f Positive"}}, "Result_is_PAN_&_P": {"f_Positive": "Result is PAN & P.f Positive", "f_Negative": "Result is PAN & P.f Negative"}, "Result_is_PAN_Positive_&_P": {"f_Negative": "Result is PAN Positive & P.f Negative"}, "Result_is_PAN_Negative_&_P": {"f_Positive": "Result is PAN Negative & P.f Positive"}, "Result_is_SYPHILIS_Positive": "Result is <PERSON>yphi<PERSON>", "Result_is_SYPHILIS_Negative": "Result is Syphilis Negative", "Result_is_HEPA_HBsAg_Positive": "Result is HEPA_HBsAg Positive", "Result_is_HEPA_HBsAg_Negative": "Result is HEPA_HBsAg Negative", "Result_is_HEPA_HCV_Positive": "Result is HEPA_HCV Positive", "Result_is_HEPA_HCV_Negative": "Result is HEPA HCV Negative", "Result_is_TROPONIN_Positive": "Result is Troponin Positive", "Result_is_TROPONIN_Negative": "Result is Troponin Negative", "Result_is_DENGUE_NS1_Positive": "Result is Dengue NS1 Positive", "Result_is_DENGUE_NS1_Negative": "Result is Dengue NS1 Negative", "Result_is_PREGNANCY_HCG_Positive": "Result is Pregnancy hCG Positive", "Result_is_PREGNANCY_HCG_Negative": "Result is Pregnancy hCG Negative", "Result_is_HIV_I_&_HIV_II_Positive": "Result is HIV-I & HIV-II Positive", "Result_is_HIV_I_Positive_&_HIV_II_Negative": "Result is HIV-I Positive & HIV-II Negative", "Result_is_HIV_I_Negative_&_HIV_II_Positive": "Result is HIV-I Negative & HIV-II Positive", "Result_is_HIV_I_&_HIV_II_Negative": "Result is HIV-I & HIV-II Negative", "Result_is_Dengue_IgG_&_Dengue_IgM_Positive": "Result is Dengue IgG & Dengue IgM Positive", "Result_is_Dengue_IgG_Positive_&_Dengue_IgM_Negative": "Result is Dengue IgG Positive & Dengue IgM Negative", "Result_is_Dengue_IgG_Negative_&_Dengue_IgM_Positive": "Result is Dengue IgG Negative & Dengue IgM Positive", "Result_is_Dengue_IgG_&_Dengue_IgM_Negative": "Result is Dengue IgG & Dengue IgM Negative", "Result_is_MALERIA_P": {"f_Antigen_Positive": "Result is Malaria P.f Antigen Positive", "f_Antigen_Negative": "Result is Malaria P.f Antigen Negative"}, "Regg_No": "Registration Number", "OTP_verified_success": "OTP verified success", "No_Readings_Found": "No Readings Found.", "Temperature_Unit": "Temperature Unit", "Fahrenheit": "�F", "Local_completed": "Completed Local Consultation", "Remote_completed": "Completed Remote Consultation", "X-Ray": "X-Ray", "duplicate_email": "Registration failed", "SEND_USERNAME_PASSWORD_ON_EMAIL_AFTER_REGISTRATION_TEMPLATE_NOURL": "Dear $patient_name, <br><br>Thank you for registering on the ReMeDi Digital Health Platform! You can access your account using the following credentials.<br><br><PERSON>rname", "Prescription_Report": "Prescription Report", "Issue_date": "Issue Date", "Digital_Recipe": "Digital Recipe", "Identitfication_number": "Identification Number", "Contact_phone_number": "Contact phone number", "Consultation": "Consultation", "Hospital": "Hospital", "Patient_data": "Patient Data", "Social_security": "Social security", "Treatment": "Treatment", "Observation": "Observation", "Manual_Entry": "Manual Entry", "Total_wbc_count": "Total wbc count", "Neutrophils": "Neutrophils", "Lymphocytes": "Lymphocytes", "Monocytes": "Monocytes", "Eosinophils": "Eosinophils", "Basophils": "Basophils", "X_Ray_Taken": "<PERSON><PERSON><PERSON>", "X_Ray_Fees": "<PERSON><PERSON><PERSON>", "X-Ray_Viewer": "<PERSON><PERSON><PERSON>", "X-Ray_REPORT_NOT_AVAILABLE": "The report is not available for download.", "X-Ray_ALREADY_PRESCRIBED": "This X-ray has already been prescribed. To add it again, please go to 'Investigations', delete it, and then add it here.", "Treatment_Plan": "Treatment Plan", "Clinical_Observations": "Clinical Observations", "Follow_Up_Schedule": "Follow-Up Schedule", "Data_has_been_saved_successfully": "Data has been saved successfully.", "Follow_Up_Schedule_on": "Follow up schedule on", "Calender": "<PERSON><PERSON>", "This_User_Already_Exists": "This account already exist", "View_Profile": "View Profile", "ADD_ABHA_Address": "ADD ABHA Address", "Prescription_Number": "Prescription Number", "License_Number": "License Number", "Consultation1": "Consultation", "fl_Name": "Name", "Identification_number": "Identification Nnumber", "PSrNo": "SrNo", "maxReached": "Maximum reached! 2000 of 2000", "TemperatureASHA": "Temperature (ASHA+)", "param_TemperatureASHA": "Thermometer(ASHA+)", "select_doctor": "Please choose a doctor to send for review.", "Write_comment_here": "Enter your comment", "No_comments_found": "No comments available", "Print_report": "Print Report", "Failed_to_fetch_comments_Please_try_again": "Unable to fetch comments. Please try again.", "maxCharactersMessage": "Maximum characters limit reached", "Review_Note": "Review Note", "Are_You_Sure1": "Are you sure you want to send for review?", "Are_You_Sure2": "Do you want to continue without adding a review note?", "PatientId_NationalId_not_registered": "National ID or Patient ID not found. Please register the Patient.", "editprofile_bookappointment": "Edit Profile & Book Appointment", "national_id": "National ID", "Enter_Batch_Code": "Enter batch code", "Enter_The_Result": "Enter the result", "Stethoscope_ASHA": "StethoScope (ASHA+)", "param_StethoscopeASHA": "StethoScope(ASHA+)", "FetosenseFetalDoppler": "<PERSON><PERSON>(Fetosense)", "param_FetosenseFetalDoppler": "FetalDoppler(Fetosense)", "Select_Ethnicity": "Select Ethnicity", "ECG_INTERPRETATION_REPORT_GENERATION_FAILED": "ECG interpretation report generation failed. Please try again.", "No_medicines_prescribed_earlier": "No medicines prescribed earlier.", "Fetch_previous_medicine_records": "Fetch Previous Medicine Records", "Previous_medicine_prescription_already_exists": "This medicine has been already prescribed.", "Foot_Analysis_Reports": "Foot Analysis Report", "Reset_Dates": "Reset Date", "PLEASE_WAIT_ECG_INTERPRETATION_DATA_SENDING_IN_PROGRESS": "Please wait, we are sending the ECG for interpretation.", "REPORT_DOWNLOAD_NOT_AVAILABLE": "The report is not available for download.", "Failed_to_save_comment": "Failed to save comment", "Error_loading_comments_Please_try_again": "Error while loading comments. Please try again.", "Rhyth": "Rhythm", "Atrial_rate": "Atrial rate", "Ventricular_rate": "Ventricular rate", "PR": "PR interval", "QRS": "QRS duration", "QT": "QT interval", "QTc": "Corrected QT (Bazett)", "QRS_axis": "QRS axis", "Enter_Values": "Enter Values", "Enter_ECG_Interpretation": "Enter ECG Interpretation", "Ecg_interpretation_report_by": "Ecg Interpretation report by", "Ecg_interpretation_report": "Ecg Interpretation Report", "ECG_Interpretation_report_saved_successfully": "ECG Interpretation report saved successfully", "Left_Axillary_Region": "Left Axillary Region", "Right_Axillary_Region": "Right Axillary Region", "Right_Lower_Lobe": "Right Lower Lobe", "Left_Lower_Lobe": "Left Lower Lobe", "Right_Upper_Lobe": "Right Upper Lobe", "Left_Upper_Lobe": "Left Upper Lobe", "Apex_Mitral_Area": "Apex (Mitral area)", "Base_Left_Pulmonic_Area": "Base Left (Pulmonic area)", "Base_Right_Aortic_Area": "Base Right (Aortic area)", "Left_Lower_Sternal_Border_Tricuspid_Zone": "Left Lower Sternal Border (Tricuspid zone)", "Posterior_Thorax_Posterior": "<PERSON><PERSON><PERSON> (Posterior)", "Anterior_Thorax_Anterior": "<PERSON><PERSON><PERSON> (Anterior)", "Stethoscope_position_wise_reading_was_not_taken_for_these_records": "Stethoscope position wise reading was not taken for these records.", "Enter_the_Stethoscope_position": "Enter the Stethoscope position", "Select_Stethoscope_position": "Select Stethoscope position", "Select_Reading": "Select Reading", "Enter_Valid_FEV1_FVC_rate": "Enter Valid FEV1/FVC Rate", "Enter_Valid_FEF25_75_rate": "Enter Valid FEF25_75 Rate", "Enter_Valid_FET_rate": "Enter Valid FET Rate", "Enter_Valid_FIVC_rate": "Enter Valid FIVC Rate", "Enter_Valid_PIF_rate": "Enter Valid PIF Rate", "Spiro(FEV1_FVC)": "Spiro (FEV1/FVC)", "Spiro(FEF25_75)": "Spiro (FEF25-75%)", "Spiro(FET)": "<PERSON><PERSON><PERSON> (FET)", "Spiro(FIVC)": "Spiro (FIVC)", "Spiro(PIF)": "Spiro (PIF)", "XAXIS_TIME": "Time", "Volume_Flow": "Volume-Flow", "Volume_Time": "Volume-Time", "VALID_ECG_MANUAL_ENTRY": "At least one field must contain a valid number.", "INVALID": "Invalid", "Appointment_By": "Appointment By", "Send_ECG_for_Interpretation": "Send ECG for Interpretation", "Do_you_want_to_send_this_ECG_reading_for_interpretation": "Do you want to send this ECG reading for interpretation?", "ECG_interpretation_request_sent_successfully_The_result_will_be_updated_in_the_patients_past_records_once_received": "ECG interpretation request sent successfully. The result will be updated in the patient's past records once received.", "show_nurse_detail_in_report": "Enable nurse details in report", "duplicate_nationalid": "National ID already associated with another patient. Please verify", "duplicate_nationalid_while_updating": "This National ID is linked to an existing patient record", "spiro_sensor_disconnected_successfully": "The reading couldn't be completed. Restart the device and try once more.", "Date_Time": "Date And Time", "Parameter_Notes": "Parameter Notes", "stetho_doctor_Note": "Note", "no_Reading": "No Reading", "Device": "<PERSON><PERSON>", "Reading_taken": "Reading taken", "Devices_Used": "Devices Used", "reading_completed_msg": "The test was completed successfully. The spirometer sensor will power off automatically.", "ERR_DEVICE_DISCONNECT_MIDTEST": "The device was disconnected in the middle of the test. Please reconnect it and try again.", "Center_Doctor_Wise_Report": "Center Doctor Wise Report", "DONGLE_DISCONNECT_INBETWEEN": "<PERSON><PERSON> not detected. Reconnect the dongle and try taking the reading again.", "reading_completed_msg_stetho": "The test was completed successfully. The Stethoscope sensor will power off automatically.", "cdss_module_active": "CDSS Module Active", "CDSS_Suggestion_Btn": "Fetch CDSS Meds (AIIMS)", "Risk_Factors_And_Personal_Habits": "Lifestyle & Health Risk Assessment", "Tobacco_Smoking": "Do you use tobacco (smoking)?", "Alcohol": "Do you consume alcohol?", "Routine_Physical_Exercise": "Do you exercise regularly?", "On_Medication": "Are you currently on any medication?", "Get_CDSS_Medicine": "Fetch CDSS Medicine", "Submit_CDSS_feedback": "Submit CDSS Feedback", "Feedback_report": "Recommendation Summary", "HbA1C_Mandatory_If_FBS": "When HbA1c is entered, Fasting Blood Sugar (FBS) reading is mandatory.", "cdss_assisted": "Care aligned with CDSS recommendation", "Pagination_First": "First", "Pagination_Previous": "Previous", "Pagination_Next": "Next", "Pagination_Last": "Last", "Export_PatientWiseDetails": "Patient Wise Details", "P_showing": "Showing", "P_to": "to", "P_of": "of", "P_entries": "entries", "P_filtered": "filtered", "fetchingData": "Fetching data...", "exporting": "Exporting...", "CDSS_Feedback_Accepted": "CDSS Feedback Accepted", "Consultation_Type": "Consultation Type", "Lifestle_advc": "LifeStyle Advice", "Medical_History": "Medical History"}