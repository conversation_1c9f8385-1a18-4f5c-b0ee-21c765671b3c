package io.ionic.starter;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.webkit.WebView;
import android.util.Log;
import android.widget.Toast;

import com.getcapacitor.BridgeActivity;

import org.json.JSONException;
import org.json.JSONObject;

import io.ionic.starter.plugins.MedicalDeviceCommunicationPlugin;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import io.ionic.starter.plugins.NovaIcareLauncherPlugin;

/**
 * MainActivity
 * Enhanced main activity with enterprise-level medical device communication support
 */
public class MainActivity extends BridgeActivity {

  private static final String TAG = "MainActivity";

  @Override
  public void onCreate(Bundle savedInstanceState) {
    registerPlugin(NovaIcareLauncherPlugin.class);
    registerPlugin(MedicalDeviceCommunicationPlugin.class);
    super.onCreate(savedInstanceState);

    // Enable WebView debugging in development
    WebView.setWebContentsDebuggingEnabled(true);

    // Handle incoming intent
    handleIncomingIntent(getIntent());

    Log.d(TAG, "✅ MainActivity created with Medical Device Communication Plugin");
  }

  @Override
  protected void onNewIntent(Intent intent) {
    super.onNewIntent(intent);
    setIntent(intent);
    handleIncomingIntent(intent);
    Log.d(TAG, "📥 New intent received");
  }

  /**
   * Handle incoming intents from NovaICare or other medical device apps
   */
  private void handleIncomingIntent(Intent intent) {
    if (intent == null) {
      Log.d(TAG, "⚠️ Incoming intent is null");
      return;
    }

    try {
      Log.d(TAG, "🔍 Processing incoming intent with extras:");

      if (intent.getExtras() != null) {
        for (String key : intent.getExtras().keySet()) {
          Object value = intent.getExtras().get(key);
          Log.d(TAG, "📦 Extra key: " + key + ", value: " + (value != null ? value.toString() : "null"));
        }
      } else {
        Log.d(TAG, "⚠️ Intent extras are null");
      }

      // Handle legacy SPO2 data format (backward compatibility)
      if (intent.hasExtra("spo2") && intent.hasExtra("pulse_rate")) {
        handleLegacySpo2Result(intent);
        // handleDeviceResult(intent);
        return;
      }

      // Handle legacy temperature data format (backward compatibility)
      if (intent.hasExtra("fahrenheit") && intent.hasExtra("celcius")) {
        String fahrenheit = intent.getStringExtra("fahrenheit");
        String celcius = intent.getStringExtra("celcius");
        String sensorType = intent.getStringExtra("sensor_type");

        Log.d(TAG, "🩸 Received legacy temperature → Fahrenheit: " + fahrenheit + ", Celsius: " + celcius + ", Sensor Type: " + sensorType);

        handleLegacyTemperatureResult(intent);
        return;
      }

      // Check for legacy hemoglobin result format
      if (intent.hasExtra("hemoglobin_value")) {
        String hbValue = intent.getStringExtra("hemoglobin_value");
        Log.d(TAG, "🩸 Received hemoglobin_value: " + hbValue);
        handleLegacyHemoglobinResult(intent);
        return; // ✅ Exit early — already handled
      }

      // Handle legacy SPO2 data format (backward compatibility)
      if (intent.hasExtra("spo2") && intent.hasExtra("pulse_rate")) {
        handleLegacySpo2Result(intent);
        // handleDeviceResult(intent);
        return;
      }

    
      // Handle legacy temperature data format (backward compatibility)
      if (intent.hasExtra("fahrenheit") && intent.hasExtra("celcius")) {
        handleLegacyTemperatureResult(intent);
        return;
      }
     

         if (intent.hasExtra("ecg_value") && intent.hasExtra("pulse_rate")) {
           Log.d(TAG, "🔍 Before calling handleEcgIcareIntent");
          handleEcgIcareIntent(intent);
         return;
       }
    
       if (isNovaIcareOpticalResult(intent)) {
                Log.d(TAG, " DETECTED NOVA ICARE OPTICAL READER RESULT");
                handleOpticalNovaIcareResult(intent);
                return;
            }
      if (isNovaIcareOpticalResult(intent)) {
        Log.d(TAG, " DETECTED NOVA ICARE OPTICAL READER RESULT");
        handleOpticalNovaIcareResult(intent);
        return;
      }

      // Handle new device result format
      // if (intent.hasExtra("device_type")) {
      //   handleDeviceResult(intent);
      //   return;
      // }

      // Handle generic medical device data
      // if (intent.hasExtra("medical_device_data")) {
      //   handleGenericDeviceData(intent);
      //   return;
      // }

      Log.d(TAG, "ℹ️ Intent does not contain medical device data"+ intent.getExtras());

    } catch (Exception e) {
      Log.e(TAG, "❌ Error handling incoming intent", e);
    }
  }


  /**
   * Handle legacy SPO2 result format (for backward compatibility)
   */
  private void handleLegacySpo2Result(Intent intent) {
    try {
      String spo2 = intent.getStringExtra("spo2");
      String pulseRate = intent.getStringExtra("pulse_rate");
      String batteryLevel = intent.getStringExtra("battery_level");

      Log.d(TAG, "📊 Legacy SPO2 result received: SPO2=" + spo2 + ", Pulse=" + pulseRate);

      Map<String, String> data = new HashMap<>();
      data.put("spo2", spo2);
      data.put("pulse_rate", pulseRate);
      if (batteryLevel != null) {
        data.put("battery_level", batteryLevel);
      }

      // Send to plugin
      if (MedicalDeviceCommunicationPlugin.instance != null) {
        MedicalDeviceCommunicationPlugin.instance.sendDeviceResult("PULSE_OXIMETER", data);
      }

    } catch (Exception e) {
      Log.e(TAG, "❌ Error handling legacy SPO2 result", e);
    }
  }
  private void handleLegacyTemperatureResult(Intent intent) {
    try {
      String fahrenheit = intent.getStringExtra("fahrenheit");
      String celcius = intent.getStringExtra("celcius");
      String batteryLevel = intent.getStringExtra("battery_level");

      Log.d(TAG, "🌡️ Legacy Temperature result received: °F=" + fahrenheit + ", °C=" + celcius);

      Map<String, String> data = new HashMap<>();

      if (fahrenheit != null && !fahrenheit.isEmpty()) {
        data.put("fahrenheit", fahrenheit);
        data.put("unit", "fahrenheit"); // ➕ Add unit field
      }

      if (celcius != null && !celcius.isEmpty()) {
        data.put("celcius", celcius);
        if (!data.containsKey("unit")) {
          data.put("unit", "celcius"); // Only if unit not already set
        }
      }

      if (batteryLevel != null) {
        data.put("battery_level", batteryLevel);
      }

      // Send to plugin
    if (MedicalDeviceCommunicationPlugin.instance != null) {
    MedicalDeviceCommunicationPlugin.instance.sendDeviceResult("THERMOMETER", data);
    Log.d(TAG, "✅ Sent temperature data to JS layer: " + data.toString());
}


    } catch (Exception e) {
      Log.e(TAG, "❌ Error handling legacy Temperature result", e);
    }
  }
  /**
   * Handle legacy hemoglobin result format (for backward compatibility)
   */
  private void handleLegacyHemoglobinResult(Intent intent) {
    try {
      String hemoglobinValue = intent.getStringExtra("hemoglobin_value");
      Log.d(TAG, "🩸 Legacy hemoglobin result received: " + hemoglobinValue);

      Map<String, String> data = new HashMap<>();
      if (hemoglobinValue != null) {
        data.put("hemoglobin_value", hemoglobinValue);
      }
      //send to plugin
      if (MedicalDeviceCommunicationPlugin.instance != null) {
        MedicalDeviceCommunicationPlugin.instance.sendDeviceResult("HEMOGLOBIN", data);
      }
    } catch (Exception e) {
      Log.e(TAG, "❌ Error handling legacy hemoglobin result", e);
    }

  }

  /**
   * Handle device result with device type (Streamlined to prevent conflicts)
   */
  // private void handleDeviceResult(Intent intent) {
  //   try {
  //     String deviceType = intent.getStringExtra("device_type");
  //     Log.d(TAG, "📊 Device result received for: " + deviceType);

  //     Map<String, String> data = new HashMap<>();

  //     // Extract all string extras
  //     if (intent.getExtras() != null) {
  //       for (String key : intent.getExtras().keySet()) {
  //         Object value = intent.getExtras().get(key);
  //         if (value instanceof String) {
  //           data.put(key, (String) value);
  //         }
  //       }
  //     }

  //     // Primary: Send data to MedicalDeviceCommunicationPlugin
  //     if (MedicalDeviceCommunicationPlugin.instance != null) {
  //       MedicalDeviceCommunicationPlugin.instance.sendDeviceResult(deviceType, data);
  //       Log.d(TAG, "✅ Device result sent to MedicalDeviceCommunicationPlugin");
  //     } else {
  //       Log.e(TAG, "❌ MedicalDeviceCommunicationPlugin instance is null");
  //     }

  //     // 🔁 Legacy SPO2 format handler
  //     if (intent.hasExtra("spo2") && intent.hasExtra("pulse_rate") &&
  //       NovaIcareLauncherPlugin.instance != null) {
  //       String spo2 = intent.getStringExtra("spo2");
  //       String pulseRate = intent.getStringExtra("pulse_rate");

  //       Log.d(TAG, "📲 Legacy SPO2 format detected: SPO2=" + spo2 + ", Pulse=" + pulseRate);
  //       NovaIcareLauncherPlugin.instance.sendSpo2Result(spo2, pulseRate);
  //     }

  //    // ✅ Reuse centralized legacy handler instead of calling a missing method
  //     if (intent.hasExtra("fahrenheit") && intent.hasExtra("celcius")) {
  //       Log.d(TAG, "Legacy Temperature format detected – delegating to handleLegacyTemperatureResult()");
  //       handleLegacyTemperatureResult(intent);
  //     }
  //   } catch (Exception e) {
  //     Log.e(TAG, "Error handling device result", e);
  //   }
  // }


  /**
   * Handle generic medical device data
   */
  // private void handleGenericDeviceData(Intent intent) {
  //   try {
  //     String deviceData = intent.getStringExtra("medical_device_data");
  //     String deviceType = intent.getStringExtra("device_type");

  //     Log.d(TAG, "📊 Generic device data received: " + deviceType);

  //     Map<String, String> data = new HashMap<>();
  //     data.put("raw_data", deviceData);
  //     data.put("data_format", "generic");

  //     // Send to plugin
  //     if (MedicalDeviceCommunicationPlugin.instance != null) {
  //       MedicalDeviceCommunicationPlugin.instance.sendDeviceResult(
  //         deviceType != null ? deviceType : "UNKNOWN", data);
  //     }

  //   } catch (Exception e) {
  //     Log.e(TAG, "❌ Error handling generic device data", e);
  //   }
  // }

  @Override
  public void onResume() {
    super.onResume();
    Log.d(TAG, "📱 MainActivity resumed - App is back in foreground");

    // Handle any pending intents when resuming from NovaICare
    handleIncomingIntent(getIntent());
  }

  @Override
  public void onPause() {
    super.onPause();
    Log.d(TAG, "📱 MainActivity paused - App moved to background");
  }

  @Override
  public void onStop() {
    super.onStop();
    Log.d(TAG, "📱 MainActivity stopped - App is in background but alive");
  }

  @Override
  public void onRestart() {
    super.onRestart();
    Log.d(TAG, "📱 MainActivity restarted - App returning from background");
  }

  @Override
  public void onDestroy() {
    Log.d(TAG, "🧹 MainActivity destroyed");

    // Clean up plugin instances to prevent memory leaks
    try {
      if (MedicalDeviceCommunicationPlugin.instance != null) {
        MedicalDeviceCommunicationPlugin.instance.removeAllListeners();
      }
    } catch (Exception e) {
      Log.e(TAG, "❌ Error cleaning up plugins", e);
    }

    super.onDestroy();
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    // Manually dispatch to plugins to avoid Capacitor callback signature mismatch crash
    boolean handled = false;

    if (MedicalDeviceCommunicationPlugin.instance != null) {
      try {
        MedicalDeviceCommunicationPlugin.instance.handleDeviceResultManually(requestCode, resultCode, data);
        handled = true;
      } catch (Exception e) {
        Log.e(TAG, "Error dispatching to MedicalDeviceCommunicationPlugin", e);
      }
    }

    if (!handled && NovaIcareLauncherPlugin.instance != null) {
      try {
        NovaIcareLauncherPlugin.instance.handleStethoResultManually(requestCode, resultCode, data);
        handled = true;
      } catch (Exception e) {
        Log.e(TAG, "Error dispatching to NovaIcareLauncherPlugin", e);
      }
    }

    if (!handled) {
      super.onActivityResult(requestCode, resultCode, data);
    }

    // Also handle incoming intent for legacy support
    if (data != null) {
      Log.d(TAG, "📥 Activity result received with data");
      handleIncomingIntent(data);
    }
  }

  private void handleOpticalNovaIcareResult(Intent intent) {
    String debugId = "DEBUG_" + System.currentTimeMillis();

    try {
      Log.e(TAG, debugId + " - 🔍 START: handleNovaIcareResult called"); // Use Log.e to ensure it shows

      // Extract optical reader data
      String opticalImagePath = intent.getStringExtra("opticalImagePath");
      String testName = intent.getStringExtra("testName");
      String testResult = intent.getStringExtra("testResult");
      String imageName = intent.getStringExtra("imageName");
      String sensorType = intent.getStringExtra("sensor_type");
      String imageFileUri = intent.getStringExtra("imageFileUri");

      Log.e(TAG, debugId + " -  Data extracted successfully"); // Use Log.e

      // CHECK PLUGIN INSTANCE WITH DETAILED DEBUGGING
      Log.e(TAG, debugId + " -  About to check NovaIcareLauncherPlugin.instance");
      Log.e(TAG, debugId + " -  NovaIcareLauncherPlugin class: " + NovaIcareLauncherPlugin.class);

      // if (NovaIcareLauncherPlugin.instance == null) {
      //     Log.e(TAG, debugId + " -  CRITICAL: NovaIcareLauncherPlugin.instance is NULL!");
      //     Log.e(TAG, debugId + " -  This means the plugin is not loaded properly");
      // } else {
      //     Log.e(TAG, debugId + " -  NovaIcareLauncherPlugin.instance is NOT NULL");
      //     Log.e(TAG, debugId + " -  SENDING RESULTS TO NOVA ICARE PLUGIN"); // This is your missing log!

      //     try {
      //         // Create comprehensive result object
      //         JSObject result = new JSObject();
      //         Log.e(TAG, debugId + " -  JSObject created");

      //         result.put("opticalImagePath", opticalImagePath);
      //         result.put("testName", testName);
      //         result.put("testResult", testResult);
      //         result.put("imageName", imageName);
      //         result.put("sensor_type", sensorType);
      //         result.put("imageFileUri", imageFileUri);
      //         result.put("source", "nova_icare_mainactivity");
      //         result.put("success", true);
      //         result.put("timestamp", System.currentTimeMillis());

      //         Log.e(TAG, debugId + " -  JSObject populated: " + result.toString());
      //         Log.e(TAG, debugId + " -  About to call sendNovaIcareResult...");

      //         // Send to plugin (which will forward to JavaScript)
      //         NovaIcareLauncherPlugin.instance.sendNovaIcareResult(result);

      //         Log.e(TAG, debugId + " -  sendNovaIcareResult called successfully");
      //         Log.e(TAG, debugId + " -  Results successfully sent to Nova iCare plugin");

      //     } catch (Exception sendException) {
      //         Log.e(TAG, debugId + " -  Exception during send: " + sendException.getMessage());
      //         sendException.printStackTrace();
      //     }
      // }

      // Also test MedicalDeviceCommunicationPlugin
      Log.e(TAG, debugId + " -  Checking MedicalDeviceCommunicationPlugin...");
      if (MedicalDeviceCommunicationPlugin.instance != null) {
        Log.e(TAG, debugId + " -  Also sending to MedicalDeviceCommunicationPlugin");

        Map<String, String> data = new HashMap<>();
        if (opticalImagePath != null) data.put("optical_image_path", opticalImagePath);
        if (testName != null) data.put("test_name", testName);
        if (testResult != null) data.put("test_result", testResult);
        if (imageName != null) data.put("image_name", imageName);
        if (sensorType != null) data.put("sensor_type", sensorType);
        if (imageFileUri != null) data.put("image_file_uri", imageFileUri);
        data.put("success", "true");
        data.put("timestamp", String.valueOf(System.currentTimeMillis()));

        try {
          MedicalDeviceCommunicationPlugin.instance.sendDeviceResult("OPTICAL_READER", data);
          Log.e(TAG, debugId + " -  Results also sent to medical device plugin");
        } catch (Exception medicalException) {
          Log.e(TAG, debugId + " -  Exception sending to MedicalDeviceCommunicationPlugin: " + medicalException.getMessage());
          medicalException.printStackTrace();
        }
      } else {
        Log.e(TAG, debugId + " -  MedicalDeviceCommunicationPlugin.instance is NULL");
      }

    } catch (Exception e) {
      Log.e(TAG, debugId + " - CRITICAL ERROR in handleNovaIcareResult: " + e.getMessage());
      e.printStackTrace();
    }

    Log.e(TAG, debugId + " -  END: handleNovaIcareResult completed");
  }

  private boolean isNovaIcareOpticalResult(Intent intent) {
        return intent.hasExtra("opticalImagePath") &&
               intent.hasExtra("testName") &&
               intent.hasExtra("testResult") &&
               intent.hasExtra("imageName")&&
               intent.hasExtra("imageName") &&
               intent.hasExtra("sensor_type");
 }


  private void handleEcgIcareIntent(Intent intent) {
    Log.d(TAG, "🔍 INSIDE handleIcareIntent");
    try {
        String pulseRate = intent.getStringExtra("pulse_rate");
        String ecgValue = intent.getStringExtra("ecg_value");
        String ecgFilePath = intent.getStringExtra("file_name");

        Log.d("NovaIcare", "onNewIntent: Pulse Rate = " + pulseRate);
        Log.d("NovaIcare", "onNewIntent: ECG Value = " + ecgValue);
        Log.d("NovaIcare", "onNewIntent: ECG File Path = " + ecgFilePath);

        if (ecgFilePath != null && !ecgFilePath.equals("")) {
            Log.d("NovaIcare", "onNewIntent: Inside file path check");
        } else {
            Toast.makeText((Context) this, "ECG file path is invalid", Toast.LENGTH_SHORT).show();
        }

       JSONObject data = new JSONObject();
       data.put("pulse_rate", pulseRate);
       data.put("ecg_value", ecgValue);
       data.put("file_name", ecgFilePath);
       data.put("success", true);
       if (MedicalDeviceCommunicationPlugin.instance != null) {
         Log.d("NovaIcare", "Inside MedicalDeviceCommunicationPlugin check");

          Map<String, String> dataMap = new HashMap<>();
           Iterator<String> keys = data.keys();
           while (keys.hasNext()) {
            String key = keys.next();
          dataMap.put(key, data.optString(key));
      }
          MedicalDeviceCommunicationPlugin.instance.sendDeviceResult("ECG", dataMap);
      }

    // Secondary: Handle legacy ECG format for backward compatibility only
      if (NovaIcareLauncherPlugin.instance != null) {
        Log.d(TAG, "📲 Legacy ECG format detected: Pulse=" + pulseRate+" AND  ECGVALUE:  " + ecgValue);
        NovaIcareLauncherPlugin.instance.sendECGResult(data);
      }

      } catch (JSONException e) {
        Log.e(TAG, "❌ JSON Error: " + e.getMessage());
    }

  }
    
}
