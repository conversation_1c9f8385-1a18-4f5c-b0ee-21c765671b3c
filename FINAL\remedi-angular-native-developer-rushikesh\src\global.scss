/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */


/* Core CSS required for Ionic components to work properly */

@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */

@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */

@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */


/* @import "@ionic/angular/css/palettes/dark.always.css"; */


/* @import "@ionic/angular/css/palettes/dark.class.css"; */

@import '@ionic/angular/css/palettes/dark.system.css';
// Electron modal fix
.modal-wrapper {
    z-index: 9999 !important;
}

ion-modal {
    z-index: 9999 !important;
}

// Angular Material Dialog fixes for Electron
// .cdk-overlay-container {
//     z-index: 10000 !important;
//     position: fixed !important;
//     top: 0 !important;
//     left: 0 !important;
//     width: 100% !important;
//     height: 100% !important;
//     pointer-events: none !important;
// }
// .cdk-overlay-backdrop {
//     position: fixed !important;
//     top: 0 !important;
//     left: 0 !important;
//     width: 100% !important;
//     height: 100% !important;
//     z-index: 10000 !important;
//     pointer-events: auto !important;
// }
// .cdk-overlay-pane {
//     pointer-events: auto !important;
//     z-index: 10001 !important;
// }
.mat-dialog-container {
    z-index: 10002 !important;
    position: relative !important;
}

// Custom dialog panel for SPO2 device
.spo2-dialog-panel {
    .mat-dialog-container {
        padding: 0 !important;
        overflow: hidden !important;
        border-radius: 8px !important;
    }
}

.swal-popup-small {
    font-size: 14px !important;
    padding: 1.5rem !important;
}

.swal-title-small {
    font-size: 18px !important;
}

.swal-btn-small {
    padding: 4px 12px !important;
    font-size: 13px !important;
}

.register-pt-heading {
    background-color: #037eb8;
    padding: 16px 24px;
    margin: 0 !important;
    color: #fff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.register-pt-heading .bt_close {
    color: #fff;
}


.ecg-dialog-panel .mat-mdc-dialog-surface {
    width: 900px !important;
    max-width: none !important;
}