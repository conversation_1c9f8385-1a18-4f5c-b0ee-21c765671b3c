const fs = require("fs");
const path = require("path");

const filePath = path.join(
    __dirname,
    "node_modules",
    "@stencil",
    "core",
    "internal",
    "client",
    "index.js"
);

if (!fs.existsSync(filePath)) {
    console.error("❌ Could not find Stencil index.js file.");
    process.exit(1);
}

let fileContent = fs.readFileSync(filePath, "utf8");

// Replace the entire dynamic import and its .then() handler
fileContent = fileContent.replace(
    /return import\([\s\S]*?\)\.then\([\s\S]*?\}\)/,
    `console.warn("⚠️ Dynamic import removed to avoid empty-glob warning");
return Promise.resolve({});`
);

// Remove ONLY the line that creates the glob pattern string template
fileContent = fileContent.replace(
    /`\.\/\$\{bundleId\}\.entry\.js\$\{BUILD5\.hotModuleReplacement && hmrVersionId \? \?"\?s-hmr=" \+ hmrVersionId : ""\}`/g,
    `"./noop.js"`
);

// OR if the line formatting is different (more flexible match):
fileContent = fileContent.replace(
    /`\.\/\$\{bundleId\}\.entry\.js\$\{.*?hmrVersionId.*?\}`/g,
    `"./noop.js"`
);

fs.writeFileSync(filePath, fileContent, "utf8");
console.log("✅ Fully patched: empty-glob warning should now be gone.");