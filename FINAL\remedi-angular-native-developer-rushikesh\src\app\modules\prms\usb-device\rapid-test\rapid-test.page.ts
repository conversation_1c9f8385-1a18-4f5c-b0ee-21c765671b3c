import { Component, OnInit, On<PERSON><PERSON>roy, Inject, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription, timer } from 'rxjs';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import * as CryptoJS from 'crypto-js';

// Import WebSocket service
import { WebsocketsService } from 'src/app/core/services/websocket.service';
import { AuthService } from 'src/app/core/services/auth.service';
import { ConstantService } from 'src/app/core/services/constant.service';
import { ApiService } from 'src/app/core/services/api.service';

// Define the component state enum for better state management
enum ComponentState {
  INSTRUCTIONS = 'instructions',
  TESTING = 'testing',
  RESULT = 'result'
}

@Component({
  selector: 'app-rapid-test',
  templateUrl: './rapid-test.page.html',
  styleUrls: ['./rapid-test.page.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, TranslateModule]
})
export class RapidTestPage implements OnInit, OnDestroy {
  
  showInstructions: boolean = true;
  diviceConnected: boolean = true;
  
  // UI display properties
  displayMessage = "";
  displayResponse = "";
  errorMessage = "";
  img: any;
  testName: string = "";
  testresult: any;
  resultImage: boolean = false;
  
  // Save button and API response properties
  showSaveButton: boolean = true;
  isSaving: boolean = false;
  apiSuccessMessage: string = "";
  apiErrorMessage: string = "";
  
  // Loading dots animation
  showLoadingDots: boolean = false;
  
  // Encryption constants
  readonly ENCRYPTION_KEY = "19f148fe75a9b266fd6398ccc3616a12d26bd67acfcbaa81654bfdb08c8dc51c";
  readonly ENCRYPTION_ALGORITHM = 'AES';
  
  // Test options
  testOptions = [
    { value: "HEPA_SCAN_HBsAg", label: "HEPA_SCAN_HBsAg" },
    { value: "HEPA_SCAN_HCV", label: "HEPA_SCAN_HCV" },
    { value: "SYPHILIS", label: "SYPHILIS" },
    { value: "TROPONIN_I", label: "TROPONIN_I" },
    { value: "MALERIA_P.f_P.v", label: "MALERIA_P.f_P.v" },
    { value: "MALERIA_P.f_PAN", label: "MALERIA_P.f_PAN" },
    { value: "DENGUE_NS1", label: "DENGUE_NS1" },
    { value: "DENGUE_IGG_IGM(Bhat_Biotech)", label: "DENGUE_IgG_IgM(Bhat_Biotech)" },
    { value: "DENGUE_IGG_IGM(SD_Standard)", label: "DENGUE_IgG_IgM(SD_Standard)" },
    { value: "PREGNANCY_HCG", label: "PREGNANCY_HCG" },
    { value: "HIV_TRILINE", label: "HIV_TRILINE" },
    { value: "BLOOD_GROUPING", label: "BLOOD_GROUPING" },
    { value: "Sickel_Cell", label: "Sickel_Cell" }
  ];
  
  // WebSocket subscription
  subscriptionInit: Subscription | null = null;

  constructor(
    private websocketsService: WebsocketsService,
    private cdr: ChangeDetectorRef,
    public dialogRef: MatDialogRef<RapidTestPage>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private constantSvc: ConstantService,
    private authSvc: AuthService,
    private apiSvc: ApiService,
  ) {}

  ngOnInit(): void {
    // Initialize component
  }

  ngOnDestroy(): void {
    if (this.subscriptionInit) {
      this.subscriptionInit.unsubscribe();
    }
  }

  /**
   * Main method to start rapid test
   */
  strarRapidTest(): void {
    console.log("Starting Rapid Test for:", this.testName);
    
    // Step 1: Validate test selection
    if (!this.testName) {
      this.errorMessage = "Please select a test first";
      return;
    }
    
    // Step 2: Clean up any existing subscription
    if (this.subscriptionInit) {
      this.subscriptionInit.unsubscribe();
      this.subscriptionInit = null;
    }
    
    // Step 3: Reset state
    this.resetForNewTest();
    
    // Step 4: Force change detection
    this.cdr.detectChanges();
    
    // Step 5: Send command and set up subscription
    this.setupWebSocketSubscription();
    this.sendParamJettyRappid();
  }

  /**
   * Reset component state for new test
   */
  private resetForNewTest(): void {
    this.showInstructions = false;
    this.resultImage = false;
    this.diviceConnected = true;
    this.img = null;
    this.testresult = null;
    this.showSaveButton = true;
    this.isSaving = false;
    this.showLoadingDots = false;
    this.clearMessages();
    this.clearApiMessages();
    this.displayResponse = "Initializing test...";
  }

  /**
   * Set up WebSocket subscription with proper error handling
   */
  private setupWebSocketSubscription(): void {
    this.subscriptionInit = this.websocketsService.paramValueObs$.subscribe(
      (data) => {
        // CRITICAL: Add null check first
        if (!data || typeof data !== 'string') {
          console.log("Received null or invalid data from WebSocket:", data);
          return;
        }

        console.log("WebSocket data received:", data);
        this.handleWebSocketData(data);
      },
      (error) => {
        console.error("WebSocket subscription error:", error);
        this.displayResponse = "Connection error occurred";
        this.showLoadingDots = false;
        this.cdr.detectChanges();
      }
    );
  }

  /**
   * Handle WebSocket data with improved logic
   */
  private handleWebSocketData(data: string): void {
    // Skip processing messages that don't belong to rapid test
    if (!data.includes("rapidTestResultcallingFromJar~")) {
      return;
    }

    console.log("Processing rapid test data:", data);

    // Use a switch-like pattern for better performance
    if (data.includes("rapidTestResultcallingFromJar~Device connected...")) {
      this.handleDeviceConnected();
    }
    else if (data.includes("rapidTestResultcallingFromJar~Could not open Camera")) {
      this.handleError("Could not open Camera");
    }
    else if (data.includes("rapidTestResultcallingFromJar~Something Problem With Camera")) {
      this.handleError("Something Problem With Camera");
    }
    else if (data.includes("rapidTestResultcallingFromJar~Could Not Open Source Image")) {
      this.handleError("Could Not Open Source Image");
    }
    else if (data.includes("rapidTestResultcallingFromJar~Test Type Not Implemented")) {
      this.handleError("Test Type Not Implemented");
    }
    else if (data.includes("rapidTestResultcallingFromJar~Could Not Open ROI Image")) {
      this.handleError("Could Not Open ROI Image");
    }
    else if (data.includes("rapidTestResultcallingFromJar~Directory not created")) {
      this.handleError("Directory not created");
    }
    else if (data.includes("rapidTestResultcallingFromJar~Slide not Present in tray")) {
      this.handleError("Slide not Present in tray");
    }
    else if (data.includes("rapidTestResultcallingFromJar~Result cannot be determined")) {
      this.handleError("Result cannot be determined");
    }
    else if (data.includes("rapidTestResultcallingFromJar~Optical Reader device Not Detected")) {
      this.handleError("Optical Reader device Not Detected");
    }
    else if (data.includes("rapidTestResultcallingFromJar~Some error to handle")) {
      this.handleError("Some error to handle");
    }
    else if (data.includes("rapidTestResultcallingFromJar~Device is not connected")) {
      this.handleError("Make sure device is connected and switched ON. Tray with cassette is inserted properly.");
    }
    else if (data.includes("rapidTestResultcallingFromJar~Invalid Test")) {
      this.handleError("Invalid Test");
    }
    else if (data.includes("rapidTestResultcallingFromJar~Computing result")) {
      this.handleComputingResult();
    }
    else if (data.includes("rapidTestResultcallingFromJar~Slide Not inserted Properly")) {
      this.handleError("Slide Not inserted Properly");
    }
    else if (data.includes("rapidTestResultcallingFromJar~Result is")) {
      this.handleTestResult(data);
    }

    // Force change detection after handling
    this.cdr.detectChanges();
  }

  /**
   * Handle device connected state
   */
  private handleDeviceConnected(): void {
    this.diviceConnected = false;
    this.displayResponse = "Device connected...";
    this.showLoadingDots = false;
    // Ensure we stay in testing mode
    this.showInstructions = false;
    this.resultImage = false;
  }

  /**
   * Handle computing result state
   */
  private handleComputingResult(): void {
    this.diviceConnected = false;
    this.displayResponse = "Computing result";
    this.showLoadingDots = true; // Show animated dots for computing result
    // Ensure we stay in testing mode
    this.showInstructions = false;
    this.resultImage = false;
  }

  /**
   * Handle error states
   */
  private handleError(message: string): void {
    this.diviceConnected = false;
    this.displayResponse = message;
    this.showLoadingDots = false;
    // Keep in testing mode to show error
    this.showInstructions = false;
    this.resultImage = false;
  }

  /**
   * Handle successful test result
   */
  private handleTestResult(data: string): void {
    try {
      const parts = data.split("~");
      if (parts.length >= 3) {
        this.displayResponse = parts[1];
        this.testresult = this.displayResponse;
        this.showLoadingDots = false;
        console.log("Rapid Result:", this.displayResponse);

        const imageDataUrl = parts[2];
        if (imageDataUrl && imageDataUrl.startsWith("data:image/png;base64,")) {
          this.img = imageDataUrl;
          this.resultImage = true; // This will show the result view
          this.showInstructions = false; // Ensure instructions are hidden
          this.showSaveButton = true; // Show save button
          console.log("Result image set, resultImage =", this.resultImage);
        } else {
          console.log("Invalid image format received");
          this.handleError("Invalid image data received");
        }
      } else {
        console.log("Invalid response format");
        this.handleError("Invalid response format");
      }
    } catch (error) {
      console.error("Error processing test result:", error);
      this.handleError("Error processing test result");
    }
  }

  /**
   * Handle test selection change
   */
  onTestSelects(event: any): void {
    this.testName = event.target.value;
    console.log("Selected test:", this.testName);
    this.clearMessages();
    this.clearApiMessages();
  }

  /**
   * Encrypt data using AES encryption
   */
  encrypt(input: string): string {
    try {
      const key = CryptoJS.enc.Hex.parse(this.ENCRYPTION_KEY);
      const encrypted = CryptoJS.AES.encrypt(input, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      });
      return encrypted.ciphertext.toString(CryptoJS.enc.Hex);
    } catch (error) {
      console.error("Encryption error:", error);
      throw new Error("Failed to encrypt data");
    }
  }

  /**
   * Save test results with encryption and API call
   */
  saveHIVTest(): void {
    if (!this.testresult || !this.img) {
      this.apiErrorMessage = "No test result to save";
      this.clearApiMessagesAfterDelay();
      return;
    }

    // Start saving process
    this.isSaving = true;
    this.clearApiMessages();
    
    try {
      // HARDCODED VALUES FOR TESTING
      const hardcodedPatientData = {
        consultationId: "12345",  // Replace with your test consultation ID
        patientId: "67890"        // Replace with your test patient ID
      };
      const hardcodedDomainId = "21";           // Replace with your test domain ID
      const hardcodedToken = "C150576301C2566BC54958AC1407B6FB836DB89078133DA00D5B2761B04C3518BC97255AA43469716992A61629AC54D96E2A275C2C446EA651EC265568D7F7BB"; // Replace with your test token

      console.log("Using hardcoded values for testing:");
      console.log("Patient ID:", hardcodedPatientData.patientId);
      console.log("Consultation ID:", hardcodedPatientData.consultationId);
      console.log("Domain ID:", hardcodedDomainId);
      console.log("Token:", hardcodedToken);

      // Encrypt the image data
      let encryptedImgData = this.encrypt(this.img);
      
      // Generate image name
      const imgName = `${this.testName}_${hardcodedPatientData.consultationId}_${hardcodedPatientData.patientId}.png`;

      // Prepare the data object for the API call
      let requestData = {
        action: "save_Rapid_Test_Data",
        domain: hardcodedDomainId,
        consultationId: hardcodedPatientData.consultationId,
        patientId: hardcodedPatientData.patientId,
        testName: this.testName,
        testresult: this.testresult,
        imgName: imgName,
        language: "English",
        requestFrom: "angular",
        tblname: "prms_diagnosis",
        img: encryptedImgData,
        token: hardcodedToken
      };

      console.log("API Data Payload:", requestData);

      // Make the API call
      
     // var ss="https://s3test2.remedi.co.in/RemediPRMS/RemediNovaDoctorAPI.do";
    //  this.apiSvc.postWithTextResponse(ss, requestData)
      this.apiSvc.postWithTextResponse(this.constantSvc.APIConfig.DISPLAYAPPOINTMENTS, requestData)
        .subscribe({
          next: (res: string) => {
            console.log("API Response:", res);
            this.isSaving = false;
            
            if (res === 'success' || res.includes('success')) {
              // Success response - hide save button and show success message
              this.showSaveButton = false;
              this.apiSuccessMessage = "Record saved successfully";
              this.apiErrorMessage = "";
              console.log("RapidReader Img saved ..");
              
              // Clear success message after delay
              this.clearApiMessagesAfterDelay();
            } else {
              // Failed response
               this.showSaveButton = false;
               console.log("showSaveButtonsssssssssssssss"+ this.showSaveButton);
              this.apiErrorMessage = "Error while saving record on server.";
            
              this.apiSuccessMessage = "";
              console.log("error while saving Test result on server.");
              
              // Clear error message after delay
              this.clearApiMessagesAfterDelay();
            }
            
            this.cdr.detectChanges();
          },
          error: (err: any) => {
            console.error("API call failed:", err);
            this.showSaveButton = false;
            this.isSaving = false;
            this.apiErrorMessage = "Error while saving record on server.";
            this.apiSuccessMessage = "";
            console.log("error while saving Test result on server.");
            
            // Clear error message after delay
            this.clearApiMessagesAfterDelay();
            this.cdr.detectChanges();
          }
        });

    } catch (err: any) {
      console.error("Error in saveHIVTest:", err);
      this.isSaving = false;
      this.showSaveButton = false;
      this.apiErrorMessage = "Error preparing data for save";
      this.apiSuccessMessage = "";
      this.clearApiMessagesAfterDelay();
      this.cdr.detectChanges();
    }
  }

  /**
   * Clear API messages after a delay (similar to jQuery fadeIn/fadeOut)
   */
  private clearApiMessagesAfterDelay(): void {
    timer(3000).subscribe(() => {
      this.apiSuccessMessage = "";
      this.apiErrorMessage = "";
      this.cdr.detectChanges();
    });
  }

  /**
   * Clear API messages immediately
   */
  private clearApiMessages(): void {
    this.apiSuccessMessage = "";
    this.apiErrorMessage = "";
  }

  /**
   * Send parameter to start rapid test
   */
  private sendParamJettyRappid(): void {
    console.log("Sending test command for:", this.testName);
    
    // Send the actual WebSocket message
    this.websocketsService.sendParamMessage("getTestResultFromLib~C:/TestImages/~"+this.testName+"_79392_6892.png~79392_6892~"+this.testName+"~null");
  }

  /**
   * Clear all messages
   */
  private clearMessages(): void {
    this.displayMessage = "";
    this.displayResponse = "";
    this.errorMessage = "";
  }

  /**
   * Close dialog
   */
  onDialogClose(): void {
    // Clean up subscription before closing
    if (this.subscriptionInit) {
      this.subscriptionInit.unsubscribe();
    }
    this.dialogRef.close();
     this.resetToInitialState();

  }

  /**
   * Reset to initial state (if needed)
   */
  resetToInitialState(): void {
    console.log("ddddddddddddddddddddsssssssssssssssssssssss");
    this.showInstructions = true;
    this.diviceConnected = true;
    this.resultImage = false;
    this.img = null;
    this.testresult = null;
    this.testName = "";
    this.showSaveButton = true;
    this.isSaving = false;
    this.showLoadingDots = false;
    this.clearMessages();
    this.clearApiMessages();
    
    if (this.subscriptionInit) {
      this.subscriptionInit.unsubscribe();
      this.subscriptionInit = null;
    }
    
    this.cdr.detectChanges();
  }
}