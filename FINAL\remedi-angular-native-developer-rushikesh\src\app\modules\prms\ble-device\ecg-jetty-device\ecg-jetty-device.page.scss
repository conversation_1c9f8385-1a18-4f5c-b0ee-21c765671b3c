#ecgHeader {
    padding: 10px;
}

#ecgheading {
    background-color: #007bff;
    height: 60px;
    padding: 10px;
    color: #c1c9ca;
}

.ecg-header-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.header-left,
.header-center,
.header-right {
    display: flex;
    flex-direction: column;
}

.header-left {
    flex: 1;
    align-items: flex-start;
    justify-content: center;
}

.header-center {
    flex: 2;
    justify-content: center;
}

.header-right {
    flex: 1;
    align-items: flex-end;
    justify-content: center;
}

.ecg-title {
    font-size: 20px;
    font-weight: bold;
    text-align: center;
}

.battery-main {
    margin-top: -21px;
}

.batteryContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.ecg-close-btn {
    border: none;
    background: transparent;
    color: white;
    font-size: 20px;
    cursor: pointer;
}

#ecgContent {
    display: none;
    max-height: 490px;
    overflow-x: hidden;
    overflow-y: auto;
    width: 100%;
    margin-top: 20px;
}

#ecgContents {
    border: 1px solid green;
    width: 100%;
    font-size: 14px;
}

.battery-outer {
    width: 40px;
    height: 20px;
    border: 2px solid #000;
    position: relative;
    border-radius: 3px;
}

.battery-level {
    background-color: green;
    height: 100%;
    transition: width 0.3s ease-in-out;
    min-height: 10px;
}

.battery-tip {
    width: 4px;
    height: 10px;
    background-color: #000;
    position: absolute;
    right: -6px;
    top: 5px;
    border-radius: 1px;
}

.ecgprintbtn {
    width: 100px;
}

.flexCol {
    display: flex;
    flex-direction: column;
    justify-content: center;
    // align-items: center;
}

.drop {
    animation: drop 3s forwards infinite;
}

::ng-deep img {
    overflow-clip-margin: content-box;
    overflow: clip;
    // width: -webkit-fill-available !important;
}

#ecgprintbtn {
    margin-top: 60px;
}

@media (min-width: 785px) {
    #ecgcanvas-container {
        display: block;
        box-sizing: border-box;
        margin-top: 10px;
    }
    #ecg_canvas {
        width: 900px;
        height: 500px;
        display: block;
        border: 1px solid green;
        box-sizing: border-box;
    }
}