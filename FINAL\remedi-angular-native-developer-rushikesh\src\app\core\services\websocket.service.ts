
import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { WebSocketSubject, webSocket } from 'rxjs/webSocket';
import { BehaviorSubject, Subject } from 'rxjs';
// import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root',
})
export class WebsocketsService implements OnDestroy {
  private mainSocket$: WebSocketSubject<any> | null = null;
  private bleSocket$: WebSocketSubject<any> | null = null;
  private paramSocket$: WebSocketSubject<any> | null = null;

  private readonly mainUrl = 'ws://localhost:5000';
  private readonly bleUrl = 'ws://localhost:8444/bleWS/';
  private readonly paramUrl = 'ws://localhost:8444/paramWS/';

  public valueObs$ = new BehaviorSubject<any>(null);
  public bleValueObs$ = new BehaviorSubject<any>(null);
  public paramValueObs$ = new BehaviorSubject<any>(null);
  // valueObsBleWs = new BehaviorSubject<any>([]);

//   constructor(private authService: AuthService) {}
  constructor() {}

  private createSocket(url: string, onMessage: (data: any) => void): WebSocketSubject<any> {
    const socket$ = webSocket({
      url,
      deserializer: ({ data }) => data,
      serializer: msg => msg,
      openObserver: {
        next: () => console.log(`WebSocket connected: ${url}`),
      },
      closeObserver: {
        next: () => console.log(`WebSocket disconnected: ${url}`),
      }
    });

    socket$.subscribe({
      next: onMessage,
      error: err => console.error(`WebSocket error at ${url}:`, err),
    });

    return socket$;
  }

  public connectMainSocket(): void {
    this.mainSocket$ = this.createSocket(this.mainUrl, (msg) => {
      this.valueObs$.next(msg);
    });
  }

  public sendMainMessage(message: string): void {
    this.mainSocket$?.next(message);
  }

  public disconnectMainSocket(): void {
    this.mainSocket$?.complete();
    this.mainSocket$ = null;
  }

  public connectBleSocket(): void {
    this.bleSocket$ = this.createSocket(this.bleUrl, (msg) => {
      this.bleValueObs$.next(msg);
    });
  }

  public sendBleMessage(message: string): void {
    this.bleSocket$?.next(message);
  }

  public disconnectBleSocket(): void {
    this.bleSocket$?.complete();
    this.bleSocket$ = null;
  }

  public connectParamSocket(): void {
    this.paramSocket$ = this.createSocket(this.paramUrl, (msg) => {
      this.paramValueObs$.next(msg);
    });
  }

  public sendParamMessage(message: string): void {
    this.paramSocket$?.next(message);
  }

  public disconnectParamSocket(): void {
    this.paramSocket$?.complete();
    this.paramSocket$ = null;
  }

  public getMainSocketInstance(): WebSocketSubject<any> | null {
    return this.mainSocket$;
  }

  public getBleSocketInstance(): WebSocketSubject<any> | null {
    return this.bleSocket$;
  }

  public getParamSocketInstance(): WebSocketSubject<any> | null {
    return this.paramSocket$;
  }

  ngOnDestroy(): void {
    this.disconnectMainSocket();
    this.disconnectBleSocket();
    this.disconnectParamSocket();
  }
}
