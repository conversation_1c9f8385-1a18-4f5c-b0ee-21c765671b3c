import {
    DeviceType,
    PlatformType,
    AndroidIntentConfig,
    WebSocketConfig,
    CommunicationMethod
} from '../interfaces/medical-device.interface';

/**
 * Medical Device Configuration
 * Enterprise-level configuration for cross-platform medical device communication
 */

export class MedicalDeviceConfig {

    /**
     * NovaICare Android App Configuration
     */
    static readonly NOVA_ICARE_ANDROID: AndroidIntentConfig = {
        packageName: 'com.neurosynaptic.nova_icare',
        className: 'com.neurosynaptic.nova_icare',
        activityName: 'com.neurosynaptic.ble.sensors.PulseOxiMeterSensor1',
        extras: {
            USE_SENSOR: true,
            useflag: '0'
        }
    };

    /**
     * Jetty Server WebSocket Configuration
     */
    static readonly JETTY_WEBSOCKET_CONFIG: { [key: string]: WebSocketConfig } = {
        development: {
            url: 'ws://localhost:8444',
            protocols: ['medical-device-protocol'],
            reconnectInterval: 3000,
            maxReconnectAttempts: 5
        },
        production: {
            url: 'wss://s3test2.remedi.co.in:8444',
            protocols: ['medical-device-protocol'],
            reconnectInterval: 5000,
            maxReconnectAttempts: 3
        },
        staging: {
            url: 'wss://staging.remedi.co.in:8444',
            protocols: ['medical-device-protocol'],
            reconnectInterval: 4000,
            maxReconnectAttempts: 4
        }
    };

    /**
     * Device-specific WebSocket endpoints
     */
    static readonly WEBSOCKET_ENDPOINTS = {
        [DeviceType.PULSE_OXIMETER]: '/bleWS/',
        [DeviceType.STETHOSCOPE]: '/audioWS/',
        [DeviceType.THERMOMETER]: '/tempWS/',
        [DeviceType.BLOOD_PRESSURE]: '/bpWS/',
        [DeviceType.ECG]: '/ecgWS/',
        [DeviceType.OPTICAL_READER]: '/opticalWS/',
        [DeviceType.HEMOGLOBIN]: '/hbWS/'
    };

    /**
     * Platform-specific communication methods
     */
    static readonly PLATFORM_COMMUNICATION_MAP: {
        [key in PlatformType]: {
            [key in DeviceType]: CommunicationMethod
        }
    } = {
            [PlatformType.ANDROID]: {
                [DeviceType.PULSE_OXIMETER]: CommunicationMethod.ANDROID_INTENT,
                [DeviceType.STETHOSCOPE]: CommunicationMethod.ANDROID_INTENT,
                [DeviceType.THERMOMETER]: CommunicationMethod.ANDROID_INTENT,
                [DeviceType.BLOOD_PRESSURE]: CommunicationMethod.ANDROID_INTENT,
                [DeviceType.ECG]: CommunicationMethod.ANDROID_INTENT,
                [DeviceType.OPTICAL_READER]: CommunicationMethod.ANDROID_INTENT,
                [DeviceType.HEMOGLOBIN]: CommunicationMethod.ANDROID_INTENT
            },
            [PlatformType.WEB]: {
                [DeviceType.PULSE_OXIMETER]: CommunicationMethod.WEBSOCKET,
                [DeviceType.STETHOSCOPE]: CommunicationMethod.WEBSOCKET,
                [DeviceType.THERMOMETER]: CommunicationMethod.WEBSOCKET,
                [DeviceType.BLOOD_PRESSURE]: CommunicationMethod.WEBSOCKET,
                [DeviceType.ECG]: CommunicationMethod.WEBSOCKET,
                [DeviceType.OPTICAL_READER]:CommunicationMethod.WEBSOCKET,
                [DeviceType.HEMOGLOBIN]: CommunicationMethod.WEBSOCKET
            },
            [PlatformType.ELECTRON]: {
                [DeviceType.PULSE_OXIMETER]: CommunicationMethod.WEBSOCKET,
                [DeviceType.STETHOSCOPE]: CommunicationMethod.WEBSOCKET,
                [DeviceType.THERMOMETER]: CommunicationMethod.WEBSOCKET,
                [DeviceType.BLOOD_PRESSURE]: CommunicationMethod.WEBSOCKET,
                [DeviceType.ECG]: CommunicationMethod.WEBSOCKET,
                [DeviceType.OPTICAL_READER]:CommunicationMethod.WEBSOCKET,
                [DeviceType.HEMOGLOBIN]: CommunicationMethod.WEBSOCKET
            }
        };

    /**
     * Device-specific Android intent configurations
     * Updated to match actual NovaICare implementation from reference files
     */
    static readonly ANDROID_DEVICE_INTENTS: { [key in DeviceType]: Partial<AndroidIntentConfig> } = {
        [DeviceType.PULSE_OXIMETER]: {
            activityName: 'com.neurosynaptic.ble.sensors.PulseOxiMeterSensor1',
            extras: {
                USE_SENSOR: true,
                useflag: '0'
            }
        },
        [DeviceType.STETHOSCOPE]: {
            activityName: 'com.neurosynaptic.usb.StethoSensor',
            extras: {
                USE_SENSOR: true,
                useflag: '0'
            }
        },
        [DeviceType.THERMOMETER]: {
            activityName: 'com.neurosynaptic.ble.sensors.TemperatureSensor1',
            extras: {
                USE_SENSOR: true,
                useflag: '0'
            }
        },
        [DeviceType.BLOOD_PRESSURE]: {
            activityName: 'com.neurosynaptic.ble.sensors.BloodPressureSensor1',
            extras: {
                USE_SENSOR: true,
                useflag: '0'
            }
        },
        [DeviceType.ECG]: {
            activityName: 'com.neurosynaptic.bluetooth.sensors.ECGSensor1',
            extras: {
                USE_SENSOR: true,
                useflag: '0'
            }
        },
        [DeviceType.OPTICAL_READER]: {
            activityName: 'com.neurosynaptic.usb.Optical_Reader',
            extras: {
                USE_SENSOR: true,
                useflag: '0'
            }
            
        },
        // i-assist Devices
        [DeviceType.HEMOGLOBIN]: {
            packageName: 'com.neurosynaptic.nova_iassist',
            activityName: 'com.neurosynaptic.usb.HemoglobinSensor',
            extras: { USE_SENSOR: true, useflag: '0' }
        }
    } ;
            

    /**
     * Standard intent extras that are always included (from NovaICare reference)
     * Updated to match exact reference implementation
     */
    static readonly STANDARD_INTENT_EXTRAS = {
        USE_SENSOR: 'USE_SENSOR',  // Fixed: uppercase to match reference
        CLASS_NAME: 'class_name',
        PACKAGE_NAME: 'package_name',
        LANGUAGE: 'language',
        USE_FLAG: 'useflag',
        PATIENT_ID: 'pid'
    };

    /**
     * Result keys for different device types (from NovaICare reference)
     */
    static readonly DEVICE_RESULT_KEYS = {
        [DeviceType.PULSE_OXIMETER]: {
            value: 'spo2',
            pulseRate: 'pulse_rate',
            error: 'error',
            fileName: 'file_name',
            sensorType: 'sensor_type'
        },
        [DeviceType.THERMOMETER]: {
            celcius: 'celcius',
            fahrenheit: 'fahrenheit',
            sensorType: 'sensor_type'
        },
        [DeviceType.BLOOD_PRESSURE]: {
            systolic: 'systolic',
            diastolic: 'diastolic',
            pulseRate: 'pulse_rate',
            error: 'error',
            sensorType: 'sensor_type'
        },
        [DeviceType.STETHOSCOPE]: {
            fileName: 'Stetho_Reading',
            value: 'stetho_value',
            sensorType: 'sensor_type'
        },
        [DeviceType.ECG]: {
            pulseRate: 'pulse_rate',
            lead1: 'ecg_lead1',
            lead2: 'ecg_lead2',
            lead3: 'ecg_lead3',
            avr: 'ecg_avr',
            avl: 'ecg_avl',
            avf: 'ecg_avf',
            v1: 'ecg_v1',
            v2: 'ecg_v2',
            v3: 'ecg_v3',
            v4: 'ecg_v4',
            v5: 'ecg_v5',
            v6: 'ecg_v6',
            fileName: 'file_name',
            value: 'ecg_value',
            sensorType: 'sensor_type'
        },
        [DeviceType.HEMOGLOBIN]: {
            hemoglobin_value: 'hemoglobin_value',
        }
    };

    /**
     * Sensor type constants used by NovaICare (from reference implementation)
     */
    static readonly NOVA_ICARE_SENSOR_TYPES = {
        THERMOMETER: 'Thermometer',
        PULSE_OXIMETER: 'Pulse Oximeter',
        BLOOD_PRESSURE: 'Blood Pressure',
        ECG: 'ECG',
        STETHOSCOPE: 'StethoScope',
        SPIROMETER: 'SpiroMeter',
        FETAL_DOPPLER: 'Fetal Doppler',
        OPTICAL_READER:'optical_reader',
        HEMOGLOBIN: 'Hemoglobin'
    };

    /**
     * Default timeout configurations (in milliseconds)
     */
    static readonly TIMEOUTS = {
        CONNECTION_TIMEOUT: 30000,      // 30 seconds
        RESPONSE_TIMEOUT: 60000,        // 1 minute
        WEBSOCKET_PING_INTERVAL: 30000, // 30 seconds
        ANDROID_INTENT_TIMEOUT: 120000, // 2 minutes
        RETRY_DELAY: 2000,              // 2 seconds
        MAX_RETRY_ATTEMPTS: 3
    };

    /**
     * WebSocket message types
     */
    static readonly WEBSOCKET_MESSAGE_TYPES = {
        DEVICE_CONNECT: 'device_connect',
        DEVICE_DISCONNECT: 'device_disconnect',
        DEVICE_DATA: 'device_data',
        DEVICE_STATUS: 'device_status',
        DEVICE_ERROR: 'device_error',
        PING: 'ping',
        PONG: 'pong',
        AUTHENTICATION: 'auth',
        SESSION_START: 'session_start',
        SESSION_END: 'session_end'
    };

    /**
     * Android intent result codes
     */
    static readonly ANDROID_RESULT_CODES = {
        SUCCESS: 'RESULT_OK',
        CANCELLED: 'RESULT_CANCELED',
        ERROR: 'RESULT_ERROR',
        TIMEOUT: 'RESULT_TIMEOUT',
        DEVICE_NOT_FOUND: 'DEVICE_NOT_FOUND',
        PERMISSION_DENIED: 'PERMISSION_DENIED'
    };

    /**
     * Error codes for standardized error handling
     */
    static readonly ERROR_CODES = {
        PLATFORM_NOT_SUPPORTED: 'PLATFORM_NOT_SUPPORTED',
        DEVICE_NOT_SUPPORTED: 'DEVICE_NOT_SUPPORTED',
        CONNECTION_FAILED: 'CONNECTION_FAILED',
        TIMEOUT: 'TIMEOUT',
        PERMISSION_DENIED: 'PERMISSION_DENIED',
        DEVICE_NOT_FOUND: 'DEVICE_NOT_FOUND',
        INVALID_CONFIGURATION: 'INVALID_CONFIGURATION',
        WEBSOCKET_ERROR: 'WEBSOCKET_ERROR',
        ANDROID_INTENT_ERROR: 'ANDROID_INTENT_ERROR',
        DATA_PARSING_ERROR: 'DATA_PARSING_ERROR',
        AUTHENTICATION_FAILED: 'AUTHENTICATION_FAILED'
    };

    /**
     * Get Android intent configuration for a specific device
     */
    static getAndroidIntentConfig(deviceType: DeviceType): AndroidIntentConfig {
        const baseConfig = this.NOVA_ICARE_ANDROID;
        const deviceConfig = this.ANDROID_DEVICE_INTENTS[deviceType];

        return {
            ...baseConfig,
            ...deviceConfig,
            extras: {
                ...baseConfig.extras,
                ...deviceConfig.extras
            }
        };
    }

    /**
     * Get WebSocket configuration for environment
     */
    static getWebSocketConfig(environment: 'development' | 'production' | 'staging' = 'development'): WebSocketConfig {
        return this.JETTY_WEBSOCKET_CONFIG[environment];
    }

    /**
     * Get WebSocket URL for specific device and environment
     */
    static getWebSocketUrl(deviceType: DeviceType, environment: 'development' | 'production' | 'staging' = 'development'): string {
        const config = this.getWebSocketConfig(environment);
        const endpoint = this.WEBSOCKET_ENDPOINTS[deviceType];
        return `${config.url}${endpoint}`;
    }

    /**
     * Get communication method for platform and device
     */
    static getCommunicationMethod(platform: PlatformType, deviceType: DeviceType): CommunicationMethod {
        return this.PLATFORM_COMMUNICATION_MAP[platform][deviceType];
    }

    /**
     * Validate device configuration
     */
    static validateDeviceConfig(platform: PlatformType, deviceType: DeviceType): boolean {
        try {
            const communicationMethod = this.getCommunicationMethod(platform, deviceType);

            switch (communicationMethod) {
                case CommunicationMethod.ANDROID_INTENT:
                    const androidConfig = this.getAndroidIntentConfig(deviceType);
                    return !!(androidConfig.packageName && androidConfig.activityName);

                case CommunicationMethod.WEBSOCKET:
                    const wsConfig = this.getWebSocketConfig();
                    return !!(wsConfig.url);

                default:
                    return false;
            }
        } catch (error) {
            console.error('Device configuration validation failed:', error);
            return false;
        }
    }

    /**
     * Get default device launch options
     */
    static getDefaultLaunchOptions(deviceType: DeviceType): Partial<any> {
        return {
            deviceType,
            timeout: this.TIMEOUTS.ANDROID_INTENT_TIMEOUT,
            retryAttempts: this.TIMEOUTS.MAX_RETRY_ATTEMPTS,
            language: 'en'
        };
    }
}

/**
 * Environment-specific configurations
 */
export const MEDICAL_DEVICE_ENVIRONMENTS = {
    development: {
        jettyServer: {
            host: 'localhost',
            port: 8444,
            secure: false
        },
        logging: {
            level: 'debug',
            enableConsole: true,
            enableRemote: false
        }
    },
    production: {
        jettyServer: {
            host: 's3test2.remedi.co.in',
            port: 8444,
            secure: true
        },
        logging: {
            level: 'error',
            enableConsole: false,
            enableRemote: true
        }
    },
    staging: {
        jettyServer: {
            host: 'staging.remedi.co.in',
            port: 8444,
            secure: true
        },
        logging: {
            level: 'info',
            enableConsole: true,
            enableRemote: true
        }
    }
};
