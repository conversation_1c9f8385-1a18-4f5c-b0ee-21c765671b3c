<div style="padding: 16px;">
    <h2>Centralized Medical Device Testing</h2>
    <p><strong>Current Platform:</strong> {{ getCurrentPlatform() }}</p>

    <button (click)="launchPulseOximeter()" style="background-color: #007bff; color: white; padding: 12px 16px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">SPO2</button>
    <!-- Device Launch Buttons -->
    <div style="margin-bottom: 20px;">
        <h3>Launch Devices</h3>
        @if (isLoadingDevices) {
        <div style="text-align: center; padding: 20px;">
            <p>🔍 Checking device availability...</p>
            <p style="color: #6c757d; font-size: 12px;">This may take a few seconds</p>
        </div>
        } @else {
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-bottom: 10px;">

            @if (isDeviceAvailable('pulse_oximeter')) { 🫀 Pulse Oximeter } @if (isDeviceAvailable('thermometer')) {
            <button (click)="launchThermometer()" style="background-color: #28a745; color: white; padding: 12px 16px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">
        🌡️ Thermometer
      </button> } @if (isDeviceAvailable('blood_pressure')) {
            <button (click)="launchBloodPressure()" style="background-color: #dc3545; color: white; padding: 12px 16px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">
        🩺 Blood Pressure
      </button> } @if (isDeviceAvailable('ecg')) {
            <button (click)="launchECG()" style="background-color: #fd7e14; color: white; padding: 12px 16px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">
        📈 ECG
        </button>
            <!-- </button> } @if (isDeviceAvailable('stethoscope')) { -->
            <!-- <button (click)="launchStethoscopeViaCentralized()" style="background-color: #6f42c1; color: white; padding: 12px 16px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">
        🔊 Stethoscope (Centralized)
      </button> } -->
            }
            <!-- Legacy Stethoscope - Always show as fallback -->
            <button (click)="launchStethoscope()" style="background-color: #6c757d; color: white; padding: 12px 16px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">
        🔊 Stethoscope (Legacy)
      </button>
        </div>

        @if (availableDevices.length === 0) {
        <div style="padding: 16px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; color: #856404;">
            <h4 style="margin-top: 0;">⚠️ No devices available</h4>
            <p>Please ensure:</p>
            <ul>
                <li>NovaICare app is installed</li>
                <li>Device activities are available</li>
                <li>App has necessary permissions</li>
            </ul>
            <p><strong>Note:</strong> Legacy Stethoscope button is still available for testing.</p>
        </div>
        } @else {
        <p style="color: #28a745; font-size: 12px;">✅ {{ availableDevices.length }} device(s) available</p>
        } }
    </div>

    <!-- Data Management Buttons -->
    <div style="margin-bottom: 20px;">
        <h3>Data Management</h3>
        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
            <button (click)="clearAllDeviceData()" style="background-color: #dc3545; color: white; padding: 8px 12px; border: none; border-radius: 4px; font-size: 12px; cursor: pointer;">
        Clear All Data
      </button>
        </div>
    </div>

    <!-- Device Data Display -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">

        <!-- Pulse Oximeter Results -->
        <div style="border: 1px solid #007bff; padding: 16px; border-radius: 8px; background-color: #f8f9fa;">
            <h3 style="color: #007bff; margin-top: 0;">🫀 Pulse Oximeter</h3>
            @if (resultdata) {
            <p><strong>SpO₂:</strong> {{ resultdata.spo2 }}%</p>
            <p><strong>Pulse Rate:</strong> {{ resultdata.pulse_rate }} bpm</p>
            @if (resultdata.batteryLevel) {
            <p><strong>Battery:</strong> {{ resultdata.batteryLevel }}%</p>
            } @if (resultdata.signalQuality) {
            <p><strong>Signal Quality:</strong> {{ resultdata.signalQuality }}</p>
            }
            <p><strong>Source:</strong> {{ resultdata.source }}</p>
            <p><strong>Timestamp:</strong> {{ resultdata.timestamp | date:'medium' }}</p>
            @if (resultdata.error) {
            <p style="color: red;"><strong>Error:</strong> {{ resultdata.error }}</p>
            } } @else {
            <p style="color: #6c757d; font-style: italic;">No data available</p>
            }
        </div>

        <!-- Thermometer Results -->
        <div style="border: 1px solid #28a745; padding: 16px; border-radius: 8px; background-color: #f8f9fa;">
            <h3 style="color: #28a745; margin-top: 0;">🌡️ Thermometer</h3>
            <!-- @if (thermometerData) { /
            <p><strong>Temperature:</strong> {{ thermometerData.temperature }}° {{ thermometerData.unit | titlecase }}</p>
            @if (thermometerData.batteryLevel) {
            <p><strong>Battery:</strong> {{ thermometerData.batteryLevel }}%</p>
            }
            <p><strong>Timestamp:</strong> {{ thermometerData.timestamp | date:'medium' }}</p>
            } @else {
            <p style="color: #6c757d; font-style: italic;">No data available</p>
            } -->
        </div>

        <!-- Blood Pressure Results -->
        <div style="border: 1px solid #dc3545; padding: 16px; border-radius: 8px; background-color: #f8f9fa;">
            <h3 style="color: #dc3545; margin-top: 0;">🩺 Blood Pressure</h3>
            @if (bloodPressureData) {
            <p><strong>Systolic:</strong> {{ bloodPressureData.systolic }} mmHg</p>
            <p><strong>Diastolic:</strong> {{ bloodPressureData.diastolic }} mmHg</p>
            <p><strong>Pulse:</strong> {{ bloodPressureData.pulse }} bpm</p>
            @if (bloodPressureData.batteryLevel) {
            <p><strong>Battery:</strong> {{ bloodPressureData.batteryLevel }}%</p>
            }
            <p><strong>Timestamp:</strong> {{ bloodPressureData.timestamp | date:'medium' }}</p>
            } @else {
            <p style="color: #6c757d; font-style: italic;">No data available</p>
            }
        </div>

        <!-- ECG Results -->
        <div style="border: 1px solid #fd7e14; padding: 16px; border-radius: 8px; background-color: #f8f9fa;">
            <h3 style="color: #fd7e14; margin-top: 0;">📈 ECG</h3>
            @if (ecgData) {
            <p><strong>Heart Rate:</strong> {{ ecgData.heartRate }} bpm</p>
            <p><strong>Duration:</strong> {{ ecgData.duration }} seconds</p>
            <p><strong>Leads:</strong> {{ ecgData.leads?.join(', ') || 'N/A' }}</p>
            <p><strong>Waveform Points:</strong> {{ ecgData.waveformData.length || 0 }}</p>
            <p><strong>Timestamp:</strong> {{ ecgData.timestamp | date:'medium' }}</p>
            } @else {
            <p style="color: #6c757d; font-style: italic;">No data available</p>
            }
        </div>

        <!-- Stethoscope Results -->
        <div style="border: 1px solid #6f42c1; padding: 16px; border-radius: 8px; background-color: #f8f9fa;">
            <h3 style="color: #6f42c1; margin-top: 0;">🔊 Stethoscope</h3>
            @if (stethoscopeData) {
            <p><strong>Duration:</strong> {{ stethoscopeData.duration }} seconds</p>
            <p><strong>Sample Rate:</strong> {{ stethoscopeData.sampleRate }} Hz</p>
            @if (stethoscopeData.heartRate) {
            <p><strong>Heart Rate:</strong> {{ stethoscopeData.heartRate }} bpm</p>
            }
            <p><strong>Audio Data Size:</strong> {{ stethoscopeData.audioData.byteLength || 0 }} bytes</p>
            <p><strong>Timestamp:</strong> {{ stethoscopeData.timestamp | date:'medium' }}</p>
            } @else {
            <p style="color: #6c757d; font-style: italic;">No data available</p>
            }
        </div>
    </div>

    <!-- Debug Information -->
    <div style="margin-top: 20px; padding: 16px; background-color: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6;">
        <h3>Debug Information</h3>
        <p><strong>Platform:</strong> {{ getCurrentPlatform() }}</p>
        @if (!isLoadingDevices) {
        <p><strong>Available Devices:</strong> {{ availableDevices.length > 0 ? availableDevices.join(', ') : 'None' }}</p>
        <p><strong>Device Check Status:</strong> ✅ Complete</p>
        } @else {
        <p><strong>Device Check Status:</strong> 🔍 Loading...</p>
        }
    </div>
</div>