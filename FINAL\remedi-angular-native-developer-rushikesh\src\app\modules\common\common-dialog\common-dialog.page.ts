import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-common-dialog',
  standalone: true,
  templateUrl: './common-dialog.page.html',
  styleUrls: ['./common-dialog.page.scss'],
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule
  ],
})
export class CommonDialogPage {
  userInput: string = '';

  constructor(
    public dialogRef: MatDialogRef<CommonDialogPage>,
    @Inject(MAT_DIALOG_DATA) public data: { title: string; message: string }
  ) {}

  confirm() {
    this.dialogRef.close(this.userInput); // Pass back the ngModel input
  }

  cancel() {
    this.dialogRef.close(); // Close without data
  }
}
