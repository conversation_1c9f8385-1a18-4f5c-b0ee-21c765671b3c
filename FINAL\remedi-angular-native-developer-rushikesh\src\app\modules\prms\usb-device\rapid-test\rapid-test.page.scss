// rapid-test.page.scss - Compact Layout

.rapid-test-page {
  width: 100%;
  background-color: transparent; 
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  display: flex;
  flex-direction: column;
  height: auto;
}

// Header - Compact
.rapid-test-header {
  background: linear-gradient(135deg, #3880ff, #5260ff);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .header-content {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 20px;
    position: relative;
    
    .page-title {
      margin: 0;
      text-align: center;
      font-size: 1.4rem;
      font-weight: 600;
    }
    
    .close-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background-color 0.2s ease;
      position: absolute;
      right: 15px;
      
      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
      
      .close-icon {
        font-size: 20px;
        color: white;
        font-weight: bold;
      }
    }
  }
}

// Main content - Very compact
.rapid-test-content1 {
  padding: 12px 25px;
  display: flex;
  background-color: transparent;
  flex-direction: column;
  gap: 16px; // Slightly increased gap
  overflow-y: auto;
  flex-shrink: 0;
}

// Instructions container - Compact with slightly more spacing
.instructions-container {
  .instruction-header {
    font-weight: 600;
    margin: 0 0 15px 0; // Slightly increased bottom margin
    color: #0a0a0a;
    font-size: 0.9rem;
  }
  
  .instruction-list {
    .instruction-item {
      display: flex;
      align-items: center;
      margin-bottom: 5px; // Slightly increased spacing between instructions
      font-size: 0.85rem;
      color: #4a5568;
      line-height: 1.3; // Improved line height
      
      .step-number {
        font-weight: 600;
        color: #2d3748;
        margin-right: 8px; // Slightly more space
        min-width: 18px;
      }
      
      .step-text {
        line-height: 1.3;
      }
    }
  }
}

// Test selection container - Compact
.test-selection-container {
  margin-bottom: 0;
  
  .test-select-row {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .select-label {
      font-weight: 600;
      color: #0a0a0a;
      white-space: nowrap;
      font-size: 0.9rem;
    }
    
    .test-selector {
      flex: 1;
      padding: 3px 3px;
      border: 1px solid #d1d5db;
      color: black;
      border-radius: 4px;
      font-size: 0.9rem;
      margin-right: 100px;
      background:transparent;
      cursor: pointer;
      transition: border-color 0.2s ease;
      
      &:focus {
        outline: none;
        border-color: #3182ce;
        box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.1);
      }
      
      option {
        padding: 6px;
      }
    }
  }
}

// Footer - No extra space
.rapid-test-footer {
  background-color: transparent;
  border-top: 1px solid #e2e8f0;
  margin: 0;
  
  .footer-line {
    display: none; // Remove the line to save space
  }
  
  .footer-content {
    padding: 8px 25px 8px 25px; // Minimal padding
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
}

.start-test-btn, .save-result-btn {
  background: #3182ce;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 25px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    background: #2c5282;
    transform: translateY(-1px);
  }
  
  &:disabled {
    background: #a0aec0;
    cursor: not-allowed;
    transform: none;
  }
}

.save-result-btn {
  background: #38a169;
  
  &:hover:not(:disabled) {
    background: #2f855a;
  }
}

// Error message - Compact
.error-message {
  background: #fff5f5;
  border-left: 4px solid #e53e3e;
  padding: 8px 12px;
  border-radius: 6px;
  margin-top: 8px;
  color: #c53030;
  font-weight: 500;
  font-size: 0.85rem;
}

// Success message styling
.success-message {
  background: #f0fff4;
  border-left: 4px solid #38a169;
  padding: 8px 12px;
  border-radius: 6px;
  margin-top: 8px;
  color: #2f855a;
  font-weight: 500;
  font-size: 0.85rem;
  animation: fadeInOut 3s ease-in-out;
  text-align: center;
}

// API Error message styling
.api-error-message {
  background: #fff5f5;
  border-left: 4px solid #e53e3e;
  padding: 8px 12px;
  border-radius: 6px;
  margin-top: 8px;
  color: #c53030;
  font-weight: 500;
  font-size: 0.85rem;
  animation: fadeInOut 3s ease-in-out;
  text-align: center;
}

// Loading dots animation
.loading-dots {
  display: inline-flex;
  align-items: center;
  margin-top: 11px;
  margin-left: 5px;
  
  .dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: #3182ce;
    margin: 0 1px;
    animation: loading-wave 1.4s ease-in-out infinite both;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

@keyframes loading-wave {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: translateY(-10px); }
  20% { opacity: 1; transform: translateY(0); }
  80% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-10px); }
}

// Loading state
.rapid-test-content2 {
  padding: 20px;
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    
    .loading-card {
      width: 100%;
      max-width: 400px;
      background: transparent;
      border-radius: 8px;
      //box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .card-content {
        padding: 0;
        
        .loading-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          padding: 30px 20px;
          
          .loading-text {
            h3 {
              margin: 0 0 8px 0;
              font-weight: 600;
              color: #2d3748;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            
            .error-text {
              margin: 8px 0 0 0;
              color: #e53e3e;
              font-weight: 500;
            }
            
            p {
              margin: 0;
              color: #718096;
            }
          }
        }
      }
    }
  }
}

// Result section
.rapid-test-content3 {
  padding: 20px 20px 0 20px; // Remove bottom padding since footer will handle it
  
  .result-section {
    .result-image-card {
      background-color: transparent;
      border-radius: 8px;
      overflow: hidden;
      
      .card-header {
        padding: 16px 24px 0;
        
        .card-title {
          margin: 0 0 10px 0;
          font-size: 1.3rem;
          font-weight: 600;
          color: #2d3748;
          text-align: center;
        }
      }
      
      .card-content {
        padding: 0 24px 16px; // Reduced bottom padding
        
        .image-container {
          display: flex;
          justify-content: center;
          margin: 16px 0;
          
          .result-image {
            max-width: 100%;
            max-height: 400px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }
  }
}

// Card entrance animation
.instructions-container,
.test-selection-container,
.rapid-test-footer,
.loading-card,
.result-image-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .rapid-test-content1 {
    padding: 10px 20px;
    gap: 10px;
  }
  
  .footer-content {
    padding: 8px 20px 8px 20px;
  }
  
  .header-content {
    padding: 10px 16px;
    
    .page-title {
      font-size: 1.2rem;
    }
    
    .close-btn {
      width: 32px;
      height: 32px;
      right: 12px;
      
      .close-icon {
        font-size: 18px;
      }
    }
  }
  
  .test-select-row {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 6px !important;
    
    .select-label {
      white-space: normal;
      font-size: 0.85rem;
    }
    
    .test-selector {
      width: 100%;
      font-size: 0.85rem;
    }
  }
  
  .instructions-container {
    .instruction-header {
      font-size: 0.85rem;
      margin-bottom: 8px;
    }
    
    .instruction-list {
      .instruction-item {
        font-size: 0.8rem;
        margin-bottom: 4px;
        
        .step-number {
          margin-right: 6px;
          min-width: 16px;
        }
      }
    }
  }
  
  .rapid-test-content2,
  .rapid-test-content3 {
    padding: 16px;
  }
  
  .rapid-test-content3 {
    padding: 16px 16px 0 16px;
  }
  
  .result-section .result-image-card .image-container .result-image {
    max-height: 300px;
  }
}

@media (max-width: 480px) {
  .rapid-test-content1 {
    padding: 8px 16px;
    gap: 8px;
  }
  
  .footer-content {
    padding: 6px 16px 6px 16px;
  }
  
  .header-content {
    padding: 8px 12px;
    
    .page-title {
      font-size: 1.1rem;
    }
  }
  
  .start-test-btn, .save-result-btn {
    width: 100%;
    padding: 6px 20px;
    font-size: 0.9rem;
  }
  
  .instructions-container {
    .instruction-header {
      font-size: 0.8rem;
    }
    
    .instruction-list {
      .instruction-item {
        font-size: 0.75rem;
        
        .step-number {
          min-width: 14px;
        }
      }
    }
  }
  
  .test-select-row {
    .select-label {
      font-size: 0.8rem;
    }
    
    .test-selector {
      font-size: 0.8rem;
      padding: 5px 8px;
    }
  }
  
  .rapid-test-content2,
  .rapid-test-content3 {
    padding: 12px;
  }
  
  .rapid-test-content3 {
    padding: 12px 12px 0 12px;
  }
}